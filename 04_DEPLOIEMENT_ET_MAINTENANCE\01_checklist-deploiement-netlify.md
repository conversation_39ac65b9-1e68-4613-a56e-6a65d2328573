# **01_CHECKLIST-DEPLOIEMENT-NETLIFY.MD - DÉPLOIEMENT NETLIFY**

## **OBJECTIF DU DÉPLOIEMENT NETLIFY**

Ce document fournit la procédure complète et sécurisée pour déployer une application React/TypeScript sur Netlify avec intégration continue GitHub. Netlify est la plateforme de déploiement recommandée pour les applications frontend de notre stack.

---

## **1. PRÉ-REQUIS ET VÉRIFICATIONS**

### **1.1 Vérifications Techniques Obligatoires**

**Avant tout déploiement, l'agent DOIT vérifier :**
- [ ] **Code source** : Repository Git avec historique propre
- [ ] **Build local** : `npm run build` fonctionne sans erreur
- [ ] **Tests** : Tous les tests passent (`npm run test`)
- [ ] **Linting** : Aucune erreur ESLint (`npm run lint`)
- [ ] **TypeScript** : Compilation sans erreur (`npm run type-check`)
- [ ] **Variables d'environnement** : `.env.local` configuré et `.env.example` à jour

### **1.2 Comptes et Accès**

**Prérequis d'accès :**
- [ ] **Compte GitHub** : Repository accessible et permissions appropriées
- [ ] **Compte Netlify** : Compte créé sur [netlify.com](https://netlify.com)
- [ ] **Netlify CLI** : Installé globalement (`npm install -g netlify-cli`)
- [ ] **Authentification** : `netlify login` effectué avec succès

### **1.3 Structure de Projet Validée**

**Vérification de la structure obligatoire :**
```
project-root/
├── src/                    # Code source
├── public/                 # Assets statiques
├── dist/                   # Dossier de build (généré)
├── package.json           # Dépendances et scripts
├── vite.config.ts         # Configuration Vite
├── netlify.toml           # Configuration Netlify (à créer)
├── .env.example           # Template des variables d'environnement
└── .env.local             # Variables locales (non commitées)
```

---

## **2. CONFIGURATION NETLIFY**

### **2.1 Initialisation du Site**

**Procédure d'initialisation sécurisée :**
```bash
# 1. Navigation vers le projet
cd /path/to/your/project

# 2. Vérification de l'état Git
git status
git log --oneline -5

# 3. Connexion à Netlify
netlify login

# 4. Initialisation du site
netlify init

# Réponses aux questions :
# ? What would you like to do? → Create & configure a new site
# ? Team: → [Sélectionner votre équipe]
# ? Site name (optional): → [nom-du-projet] ou laisser vide
# ? Your build command (hugo build/yarn run build/etc): → npm run build
# ? Directory to deploy (blank for current dir): → dist
# ? Netlify functions folder: → [Laisser vide ou "netlify/functions"]
```

### **2.2 Configuration netlify.toml**

**Fichier de configuration obligatoire :**
```toml
# netlify.toml
[build]
  # Commande de build
  command = "npm run build"

  # Dossier à déployer
  publish = "dist"

  # Variables d'environnement pour le build
  environment = { NODE_VERSION = "18" }

[build.processing]
  # Optimisations automatiques
  skip_processing = false

[build.processing.css]
  bundle = true
  minify = true

[build.processing.js]
  bundle = true
  minify = true

[build.processing.html]
  pretty_urls = true

[build.processing.images]
  compress = true

# Redirections pour SPA (Single Page Application)
[[redirects]]
  from = "/*"
  to = "/index.html"
  status = 200

# Headers de sécurité
[[headers]]
  for = "/*"
  [headers.values]
    X-Frame-Options = "DENY"
    X-XSS-Protection = "1; mode=block"
    X-Content-Type-Options = "nosniff"
    Referrer-Policy = "strict-origin-when-cross-origin"
    Content-Security-Policy = "default-src 'self'; script-src 'self' 'unsafe-inline' https://apis.google.com; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; font-src 'self' https://fonts.gstatic.com; img-src 'self' data: https:; connect-src 'self' https://*.firebaseio.com https://*.googleapis.com"

# Cache des assets statiques
[[headers]]
  for = "/assets/*"
  [headers.values]
    Cache-Control = "public, max-age=31536000, immutable"

# Variables d'environnement (exemple)
[context.production.environment]
  VITE_APP_ENV = "production"

[context.deploy-preview.environment]
  VITE_APP_ENV = "preview"

[context.branch-deploy.environment]
  VITE_APP_ENV = "development"
```

### **2.3 Configuration des Variables d'Environnement**

**Via l'interface Netlify :**
1. Aller sur https://app.netlify.com/sites/[your-site]/settings/deploys
2. Section "Environment variables"
3. Ajouter chaque variable :

```
VITE_FIREBASE_API_KEY=AIza...
VITE_FIREBASE_AUTH_DOMAIN=your-project.firebaseapp.com
VITE_FIREBASE_PROJECT_ID=your-project-id
VITE_FIREBASE_STORAGE_BUCKET=your-project.appspot.com
VITE_FIREBASE_MESSAGING_SENDER_ID=123456789
VITE_FIREBASE_APP_ID=1:123456789:web:abcdef
VITE_APP_VERSION=1.0.0
```

**Via Netlify CLI :**
```bash
# Ajouter une variable d'environnement
netlify env:set VITE_FIREBASE_API_KEY "your-api-key"

# Lister les variables
netlify env:list

# Importer depuis un fichier .env
netlify env:import .env.production
```

---

## **3. DÉPLOIEMENT ET INTÉGRATION CONTINUE**

### **3.1 Déploiement Initial**

**Procédure de premier déploiement :**
```bash
# 1. Vérification finale avant déploiement
npm run lint
npm run type-check
npm run test
npm run build

# 2. Commit de la configuration Netlify
git add netlify.toml
git commit -m "feat: configuration Netlify pour déploiement"

# 3. Push vers GitHub
git push origin main

# 4. Vérification du déploiement
netlify status
netlify open
```

### **3.2 Configuration GitHub Integration**

**Intégration automatique avec GitHub :**
1. **Via l'interface Netlify :**
   - Aller sur https://app.netlify.com/sites/[your-site]/settings/deploys
   - Section "Continuous Deployment"
   - "Link to Git repository"
   - Sélectionner GitHub et autoriser l'accès
   - Choisir le repository et la branche (main)

2. **Configuration des hooks :**
   - **Branch to deploy** : `main`
   - **Build command** : `npm run build`
   - **Publish directory** : `dist`
   - **Deploy previews** : Activé pour les PR
   - **Branch deploys** : Activé pour les branches de développement

### **3.3 Workflow de Déploiement**

**Processus automatisé :**
```
1. Push sur main → Déploiement production automatique
2. Push sur develop → Déploiement de branche (si configuré)
3. Pull Request → Deploy Preview automatique
4. Merge PR → Déploiement production
```

---

## **4. VÉRIFICATIONS POST-DÉPLOIEMENT**

### **4.1 Tests de Fonctionnement**

**Checklist de validation obligatoire :**
- [ ] **Accessibilité** : Site accessible via l'URL Netlify
- [ ] **Performance** : Temps de chargement < 3 secondes
- [ ] **Fonctionnalités** : Toutes les fonctionnalités principales testées
- [ ] **Responsive** : Affichage correct sur mobile/tablet/desktop
- [ ] **Console** : Aucune erreur JavaScript critique
- [ ] **Network** : Toutes les ressources chargées correctement

### **4.2 Tests de Sécurité**

**Vérifications de sécurité :**
```bash
# Test des headers de sécurité
curl -I https://your-site.netlify.app

# Vérification SSL
openssl s_client -connect your-site.netlify.app:443 -servername your-site.netlify.app

# Test de redirection SPA
curl -I https://your-site.netlify.app/non-existent-route
```

### **4.3 Monitoring Initial**

**Configuration du monitoring :**
- [ ] **Analytics** : Google Analytics configuré (si applicable)
- [ ] **Performance** : Core Web Vitals surveillés
- [ ] **Erreurs** : Monitoring des erreurs JavaScript
- [ ] **Uptime** : Surveillance de disponibilité configurée

---

## **5. TROUBLESHOOTING**

### **5.1 Problèmes Courants**

**Build failures :**
```bash
# Vérifier les logs de build
netlify logs

# Tester le build localement
npm run build

# Vérifier les variables d'environnement
netlify env:list

# Nettoyer le cache de build
netlify build --clear-cache
```

**Problèmes de routing SPA :**
```toml
# Vérifier la redirection dans netlify.toml
[[redirects]]
  from = "/*"
  to = "/index.html"
  status = 200
```

**Problèmes de performance :**
```bash
# Analyser la taille du bundle
npm run analyze

# Vérifier les optimisations Netlify
# Site settings > Build & deploy > Post processing
```

### **5.2 Commandes de Debug**

**Outils de diagnostic :**
```bash
# Statut du site
netlify status

# Logs de déploiement
netlify logs

# Test local avec Netlify Dev
netlify dev

# Déploiement manuel pour test
netlify deploy --dir=dist --prod
```

---

**⚠️ IMPORTANT :** Ce processus de déploiement Netlify est optimisé pour la stack React/TypeScript/Vite. Toute modification de la configuration doit être testée en environnement de staging avant la production. La sécurité et les performances sont prioritaires dans toute configuration.

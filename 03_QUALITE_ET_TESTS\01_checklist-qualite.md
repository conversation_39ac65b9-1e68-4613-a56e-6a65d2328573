# Checklist Qualité

Ce document définit les critères et les pratiques pour garantir la qualité du code et des livrables produits par l'agent de développement IA.

### **1\. Qualité du Code**

* \[ \] Le code est-il formaté avec Prettier ?  
* \[ \] Le linter (ESLint) ne remonte aucune erreur ou avertissement critique ?  
* \[ \] Le code est-il écrit en TypeScript et le typage est-il strict (pas de any non justifié) ?  
* \[ \] Les noms de variables, fonctions et composants sont-ils clairs et explicites ?  
* \[ \] La logique complexe est-elle accompagnée de commentaires expliquant le "pourquoi" ?  
* \[ \] Aucun code mort, commenté ou console.log de débogage ne subsiste.

### **2\. Fonctionnalité**

* \[ \] La fonctionnalité répond-elle à tous les critères du cahier des charges ou de la user story ?  
* \[ \] Tous les scénarios de test manuel associés à la fonctionnalité passent-ils avec succès ?  
* \[ \] La gestion des erreurs est-elle en place ? (ex: message clair si un appel API échoue).  
* \[ \] L'interface utilisateur réagit-elle correctement aux différentes actions (clics, saisies) ?

### **3\. Performance et Optimisation**

* \[ \] Les animations (GSAP/R3F) sont-elles fluides (viser 60 FPS) ?  
* \[ \] Les composants lourds sont-ils chargés en lazy loading si possible (React.lazy) ?  
* \[ \] Les re-rendus inutiles de composants ont-ils été évités (React.memo, useCallback) ?  
* \[ \] Le poids des images a-t-il été optimisé ?

### **4\. Sécurité**

* \[ \] Aucune clé d'API, token ou autre secret n'est écrit en dur dans le code.  
* \[ \] Toutes les données sensibles sont chargées via des variables d'environnement (import.meta.env).  
* \[ \] Les règles de sécurité Firestore/Storage ont-elles été vérifiées pour cette nouvelle fonctionnalité ?

### **5\. Esthétique et UX (Expérience Utilisateur)**

* \[ \] Le layout est-il conforme aux maquettes ?  
* \[ \] L'affichage est-il responsive et ne cause aucun débordement horizontal sur les tailles d'écran courantes (mobile, tablette, desktop) ?  
* \[ \] Les éléments interactifs (boutons, liens) ont-ils un état hover et focus clair ?  
* \[ \] Les textes sont-ils lisibles et le contraste des couleurs est-il suffisant (accessibilité) ?

## 1. Utilisation des Sources Fiables (Serveurs MCP)

*   **Prioriser les serveurs MCP (Master Control Program)** : Les agents de codage doivent considérer tous les serveurs MCP pertinents comme des sources primaires de solutions et de bibliothèques fiables.
*   **Intégration de Context7** :
    *   **Context7** est un exemple essentiel de serveur MCP à utiliser. Il est spécifiquement conçu pour fournir une documentation de code à jour et spécifique à la version pour les LLM et les éditeurs de code IA.
    *   L'utilisation de Context7 permet d'éviter des problèmes courants tels que les exemples de code obsolètes, les API "hallucinées" (qui n'existent pas) et les réponses génériques pour d'anciennes versions de paquets.
    *   En intégrant directement la documentation et les exemples de code à jour dans le contexte du LLM, Context7 aide à obtenir des réponses de code fonctionnelles, contribuant directement à la précision technique et à la qualité du code produit.
*   **Recherche active d'autres serveurs MCP** : Si la capacité via un accès web est disponible, les agents sont encouragés à rechercher activement d'autres serveurs MCP spécialisés dans les technologies de la stack du projet.
*   **Proposition d'installation de bibliothèques** : L'agent est autorisé à proposer l'installation de bibliothèques fiables découvertes sur ces serveurs, après approbation.
*   **Règles d'invocation automatique** : Il est possible de configurer des règles pour invoquer automatiquement Context7 pour les questions de code, de configuration ou de documentation, afin d'assurer une qualité constante des informations utilisées.

## 2. Compréhension du Codebase et Débogage

*   **Compréhension du code** : Augment, en tant que plateforme d'IA pour développeurs, aide à comprendre le code. L'agent doit tirer parti de ces capacités pour assurer la qualité.
*   **Aide au débogage** : Augment est conçu pour aider à déboguer les problèmes, ce qui est crucial pour maintenir la qualité. L'agent doit utiliser cette fonctionnalité activement.

## 3. Précision Technique

*   **Validation systématique** : Les réponses générées par Augment peuvent contenir des erreurs. L'agent doit donc systématiquement valider la qualité et l'exactitude des informations et du code produits.
*   **Prévention des erreurs** : L'agent doit prendre des mesures pour éviter les erreurs, telles que la vérification des informations et du code avant de les fournir.
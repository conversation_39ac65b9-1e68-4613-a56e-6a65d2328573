# 01_directives-codage.md : Directives Générales de Codage

**Objectif :** Ce document fournit des lignes directrices générales pour l'agent de codage concernant l'implémentation du code, l'utilisation des outils IA et la gestion des tâches de développement.

**Directives pour l'agent de codage :**

1.  **Cohérence avec l'architecture :** Chaque morceau de code généré doit être en parfaite adéquation avec l'architecture définie dans le chapitre `01_ARCHITECTURE_ET_SETUP`. Référez-vous aux documents pertinents (ex: `02_structure-composants-react.md`, `03_configuration-tailwind.md`, `04_setup-firebase.md`) pour les conventions et les structures.
2.  **Utilisation d'Augment Code :**
    *   Augment Code est votre plateforme IA principale pour la productivité. Utilisez ses fonctionnalités de **Chat** pour poser des questions ou obtenir des clarifications sur des concepts.
    *   Employez **Next Edit** pour les refactorisations, les modifications complexes ou les tâches répétitives, en suivant ses suggestions étape par étape.
    *   Utilisez les **Code Completions** pour accélérer l'écriture de code, en vous appuyant sur sa compréhension de la base de code existante.
3.  **Appel aux serveurs MCP (Master Control Program) :**
    *   Pour toute question nécessitant une documentation technique à jour ou des exemples de code précis pour des bibliothèques spécifiques (React, Firebase, Tailwind, etc.), utilisez la directive `use context7` (si vous opérez dans un client MCP compatible tel que Cursor, Windsurf, Claude Desktop, Augment Code, JetBrains AI Assistant ou VS Code). Cela permettra d'éviter les informations obsolètes ou les APIs inexistantes.
    *   Si vous avez la capacité via un accès web, **recherchez activement d'autres serveurs MCP** spécialisés dans les technologies de notre stack si Context7 ne fournit pas l'information requise ou si une alternative plus pertinente existe.
    *   Vous pouvez proposer l'intégration de bibliothèques fiables découvertes via ces MCP, mais toute installation nécessitera une approbation.
4.  **Clarté et maintenabilité du code :** Écrivez du code lisible, bien commenté et conforme aux meilleures pratiques de l'industrie (ex: principes SOLID, DRY, KISS). Priorisez la simplicité et l'efficacité.
5.  **Gestion des erreurs :** Implémentez une gestion d'erreurs robuste pour toutes les opérations potentiellement échouées (appels API, interactions utilisateur, etc.).
6.  **Sécurité :** Soyez conscient des implications de sécurité, notamment lors de la manipulation de données sensibles ou d'interactions avec des services externes. Ne jamais coder en dur des identifiants ou des clés API.
7.  **Auto-correction et vérification :** Avant de considérer une tâche comme terminée, effectuez une auto-vérification approfondie de votre code. Vérifiez qu'il répond aux exigences, qu'il est fonctionnel et qu'il n'introduit pas de régressions.
8.  **Documentation :** Écrivez des commentaires et des notes pour aider les autres à comprendre votre code. Cela peut inclure des explications techniques, des exemples de code, des étapes de configuration et des considérations de performance.
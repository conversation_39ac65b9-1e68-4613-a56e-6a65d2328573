# **05_AUDIT-ACCESSIBILITE.MD - CONFORMITÉ ACCESSIBILITÉ**

## **OBJECTIF DE L'ACCESSIBILITÉ**

Ce document définit les standards d'accessibilité obligatoires pour garantir que l'application soit utilisable par tous, y compris les personnes en situation de handicap. La conformité WCAG 2.1 niveau AA est un prérequis non négociable.

---

## **1. STANDARDS D'ACCESSIBILITÉ**

### **1.1 Conformité WCAG 2.1 AA**

**Principes fondamentaux :**
- **Perceptible** : L'information doit être présentable de façon perceptible
- **Utilisable** : Les composants d'interface doivent être utilisables
- **Compréhensible** : L'information et l'interface doivent être compréhensibles
- **Robuste** : Le contenu doit être suffisamment robuste

### **1.2 Niveaux de Conformité**

**Exigences par niveau :**
- **Niveau A** : Conformité de base (obligatoire)
- **Niveau AA** : Conformité standard (OBLIGATOIRE pour ce projet)
- **Niveau AAA** : Conformité avancée (recommandée si possible)

---

## **2. AUDIT TECHNIQUE**

### **2.1 Outils d'Audit Automatisé**

**Installation des outils obligatoires :**
```bash
# Outils de test d'accessibilité
npm install -D @axe-core/react
npm install -D axe-playwright
npm install -D jest-axe

# Extension navigateur recommandée
# axe DevTools (Chrome/Firefox)
```

**Configuration axe-core :**
```typescript
// src/test/setup.ts
import { configureAxe } from 'jest-axe'

// Configuration axe pour les tests
const axe = configureAxe({
  rules: {
    // Désactiver certaines règles si nécessaire (avec justification)
    'color-contrast': { enabled: true },
    'focus-order-semantics': { enabled: true },
    'keyboard-navigation': { enabled: true }
  }
})

export { axe }
```

### **2.2 Tests d'Accessibilité Automatisés**

**Tests axe-core obligatoires :**
```typescript
// components/ui/Button.a11y.test.tsx
import { render } from '@testing-library/react'
import { axe, toHaveNoViolations } from 'jest-axe'
import { Button } from './Button'

expect.extend(toHaveNoViolations)

describe('Button Accessibility', () => {
  it('should not have accessibility violations', async () => {
    const { container } = render(<Button>Click me</Button>)
    const results = await axe(container)
    expect(results).toHaveNoViolations()
  })

  it('should be accessible when disabled', async () => {
    const { container } = render(<Button disabled>Disabled</Button>)
    const results = await axe(container)
    expect(results).toHaveNoViolations()
  })

  it('should be accessible with different variants', async () => {
    const variants = ['default', 'destructive', 'outline', 'secondary', 'ghost']
    
    for (const variant of variants) {
      const { container } = render(
        <Button variant={variant as any}>Button</Button>
      )
      const results = await axe(container)
      expect(results).toHaveNoViolations()
    }
  })
})
```

**Tests de navigation clavier :**
```typescript
// components/forms/LoginForm.a11y.test.tsx
import { render, screen } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import { LoginForm } from './LoginForm'

describe('LoginForm Keyboard Navigation', () => {
  it('should support tab navigation', async () => {
    const user = userEvent.setup()
    render(<LoginForm onSubmit={vi.fn()} />)

    const emailInput = screen.getByLabelText('Email')
    const passwordInput = screen.getByLabelText('Mot de passe')
    const submitButton = screen.getByRole('button', { name: 'Se connecter' })

    // Navigation avec Tab
    await user.tab()
    expect(emailInput).toHaveFocus()

    await user.tab()
    expect(passwordInput).toHaveFocus()

    await user.tab()
    expect(submitButton).toHaveFocus()
  })

  it('should support Enter key submission', async () => {
    const user = userEvent.setup()
    const onSubmit = vi.fn()
    render(<LoginForm onSubmit={onSubmit} />)

    await user.type(screen.getByLabelText('Email'), '<EMAIL>')
    await user.type(screen.getByLabelText('Mot de passe'), 'password')
    
    // Soumettre avec Enter
    await user.keyboard('{Enter}')
    
    expect(onSubmit).toHaveBeenCalled()
  })
})
```

---

## **3. IMPLÉMENTATION ACCESSIBLE**

### **3.1 Structure Sémantique HTML**

**Utilisation correcte des éléments HTML :**
```typescript
// ✅ Correct - Structure sémantique
const ArticleCard: React.FC<{ article: Article }> = ({ article }) => (
  <article className="article-card">
    <header>
      <h2>{article.title}</h2>
      <time dateTime={article.publishedAt}>
        {formatDate(article.publishedAt)}
      </time>
    </header>
    <main>
      <p>{article.excerpt}</p>
    </main>
    <footer>
      <nav aria-label="Actions de l'article">
        <Button variant="outline">Lire la suite</Button>
        <Button variant="ghost">Partager</Button>
      </nav>
    </footer>
  </article>
)

// ❌ Incorrect - Divs génériques
const ArticleCard: React.FC<{ article: Article }> = ({ article }) => (
  <div className="article-card">
    <div>
      <div>{article.title}</div>
      <div>{formatDate(article.publishedAt)}</div>
    </div>
    <div>{article.excerpt}</div>
    <div>
      <div>Lire la suite</div>
      <div>Partager</div>
    </div>
  </div>
)
```

### **3.2 Attributs ARIA Obligatoires**

**Implémentation des attributs ARIA :**
```typescript
// Composant Modal accessible
interface ModalProps {
  isOpen: boolean
  onClose: () => void
  title: string
  children: React.ReactNode
}

const Modal: React.FC<ModalProps> = ({ isOpen, onClose, title, children }) => {
  const titleId = useId()
  const descriptionId = useId()

  useEffect(() => {
    if (isOpen) {
      // Piéger le focus dans la modal
      document.body.style.overflow = 'hidden'
      return () => {
        document.body.style.overflow = 'unset'
      }
    }
  }, [isOpen])

  if (!isOpen) return null

  return (
    <div
      className="modal-overlay"
      role="dialog"
      aria-modal="true"
      aria-labelledby={titleId}
      aria-describedby={descriptionId}
      onClick={onClose}
    >
      <div
        className="modal-content"
        onClick={(e) => e.stopPropagation()}
      >
        <header className="modal-header">
          <h2 id={titleId}>{title}</h2>
          <button
            onClick={onClose}
            aria-label="Fermer la modal"
            className="modal-close"
          >
            ×
          </button>
        </header>
        <div id={descriptionId} className="modal-body">
          {children}
        </div>
      </div>
    </div>
  )
}

// Composant de navigation accessible
const Navigation: React.FC = () => (
  <nav aria-label="Navigation principale">
    <ul role="menubar">
      <li role="none">
        <a href="/" role="menuitem" aria-current="page">
          Accueil
        </a>
      </li>
      <li role="none">
        <button
          role="menuitem"
          aria-expanded={false}
          aria-haspopup="true"
          onClick={toggleSubmenu}
        >
          Produits
        </button>
        <ul role="menu" aria-label="Sous-menu Produits">
          <li role="none">
            <a href="/products/web" role="menuitem">
              Applications Web
            </a>
          </li>
        </ul>
      </li>
    </ul>
  </nav>
)
```

### **3.3 Gestion du Focus**

**Gestion appropriée du focus :**
```typescript
// Hook pour la gestion du focus
const useFocusManagement = () => {
  const focusRef = useRef<HTMLElement>(null)

  const setFocus = useCallback(() => {
    if (focusRef.current) {
      focusRef.current.focus()
    }
  }, [])

  const trapFocus = useCallback((event: KeyboardEvent) => {
    if (event.key !== 'Tab') return

    const focusableElements = focusRef.current?.querySelectorAll(
      'button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])'
    )

    if (!focusableElements?.length) return

    const firstElement = focusableElements[0] as HTMLElement
    const lastElement = focusableElements[focusableElements.length - 1] as HTMLElement

    if (event.shiftKey) {
      if (document.activeElement === firstElement) {
        lastElement.focus()
        event.preventDefault()
      }
    } else {
      if (document.activeElement === lastElement) {
        firstElement.focus()
        event.preventDefault()
      }
    }
  }, [])

  return { focusRef, setFocus, trapFocus }
}

// Composant avec gestion du focus
const SearchBox: React.FC = () => {
  const [isOpen, setIsOpen] = useState(false)
  const { focusRef, setFocus, trapFocus } = useFocusManagement()

  useEffect(() => {
    if (isOpen) {
      setFocus()
      document.addEventListener('keydown', trapFocus)
      return () => document.removeEventListener('keydown', trapFocus)
    }
  }, [isOpen, setFocus, trapFocus])

  return (
    <div ref={focusRef} className="search-box">
      <input
        type="search"
        placeholder="Rechercher..."
        aria-label="Rechercher dans le site"
        onFocus={() => setIsOpen(true)}
        onBlur={() => setIsOpen(false)}
      />
      {isOpen && (
        <div role="listbox" aria-label="Suggestions de recherche">
          {/* Résultats de recherche */}
        </div>
      )}
    </div>
  )
}
```

---

## **4. CONTRASTE ET COULEURS**

### **4.1 Ratios de Contraste Obligatoires**

**Standards WCAG 2.1 AA :**
- **Texte normal** : Ratio minimum 4.5:1
- **Texte large** (18pt+ ou 14pt+ gras) : Ratio minimum 3:1
- **Éléments d'interface** : Ratio minimum 3:1

### **4.2 Configuration Tailwind pour l'Accessibilité**

**Palette de couleurs accessible :**
```javascript
// tailwind.config.js
module.exports = {
  theme: {
    extend: {
      colors: {
        // Couleurs avec ratios de contraste validés
        primary: {
          50: '#eff6ff',   // Contraste 1.02:1 sur blanc
          100: '#dbeafe',  // Contraste 1.07:1 sur blanc
          500: '#3b82f6',  // Contraste 4.5:1 sur blanc ✅
          600: '#2563eb',  // Contraste 5.9:1 sur blanc ✅
          900: '#1e3a8a',  // Contraste 12.6:1 sur blanc ✅
        },
        success: {
          500: '#10b981',  // Contraste 4.5:1 sur blanc ✅
          600: '#059669',  // Contraste 5.9:1 sur blanc ✅
        },
        error: {
          500: '#ef4444',  // Contraste 4.5:1 sur blanc ✅
          600: '#dc2626',  // Contraste 5.9:1 sur blanc ✅
        }
      }
    }
  }
}
```

### **4.3 Tests de Contraste Automatisés**

**Validation automatique des contrastes :**
```typescript
// utils/colorContrast.test.ts
import { getContrastRatio, isAccessibleContrast } from './colorContrast'

describe('Color Contrast', () => {
  it('should validate primary color contrasts', () => {
    // Test des couleurs principales
    expect(isAccessibleContrast('#3b82f6', '#ffffff')).toBe(true) // 4.5:1
    expect(isAccessibleContrast('#1e3a8a', '#ffffff')).toBe(true) // 12.6:1
    expect(isAccessibleContrast('#dbeafe', '#ffffff')).toBe(false) // 1.07:1
  })

  it('should calculate contrast ratios correctly', () => {
    expect(getContrastRatio('#000000', '#ffffff')).toBeCloseTo(21, 1)
    expect(getContrastRatio('#ffffff', '#ffffff')).toBeCloseTo(1, 1)
    expect(getContrastRatio('#3b82f6', '#ffffff')).toBeGreaterThan(4.5)
  })
})
```

---

## **5. IMAGES ET MÉDIAS**

### **5.1 Textes Alternatifs**

**Implémentation des alt texts :**
```typescript
// Composant Image accessible
interface AccessibleImageProps {
  src: string
  alt: string
  decorative?: boolean
  caption?: string
  className?: string
}

const AccessibleImage: React.FC<AccessibleImageProps> = ({
  src,
  alt,
  decorative = false,
  caption,
  className
}) => {
  const imageId = useId()
  const captionId = useId()

  return (
    <figure className={className}>
      <img
        id={imageId}
        src={src}
        alt={decorative ? '' : alt}
        aria-describedby={caption ? captionId : undefined}
        role={decorative ? 'presentation' : undefined}
      />
      {caption && (
        <figcaption id={captionId}>
          {caption}
        </figcaption>
      )}
    </figure>
  )
}

// Utilisation
const ProductCard: React.FC<{ product: Product }> = ({ product }) => (
  <div className="product-card">
    <AccessibleImage
      src={product.image}
      alt={`Photo du produit ${product.name}`}
      caption={product.imageCaption}
    />
    <h3>{product.name}</h3>
    <p>{product.description}</p>
  </div>
)
```

### **5.2 Vidéos et Audio**

**Accessibilité des médias :**
```typescript
// Composant Vidéo accessible
interface AccessibleVideoProps {
  src: string
  poster?: string
  subtitles?: string
  transcript?: string
  title: string
}

const AccessibleVideo: React.FC<AccessibleVideoProps> = ({
  src,
  poster,
  subtitles,
  transcript,
  title
}) => {
  const videoId = useId()
  const transcriptId = useId()

  return (
    <div className="video-container">
      <video
        id={videoId}
        controls
        poster={poster}
        aria-label={title}
        aria-describedby={transcript ? transcriptId : undefined}
      >
        <source src={src} type="video/mp4" />
        {subtitles && (
          <track
            kind="subtitles"
            src={subtitles}
            srcLang="fr"
            label="Français"
            default
          />
        )}
        <p>
          Votre navigateur ne supporte pas la lecture de vidéos.
          <a href={src}>Télécharger la vidéo</a>
        </p>
      </video>
      
      {transcript && (
        <details className="transcript">
          <summary>Transcription de la vidéo</summary>
          <div id={transcriptId}>
            {transcript}
          </div>
        </details>
      )}
    </div>
  )
}
```

---

## **6. FORMULAIRES ACCESSIBLES**

### **6.1 Labels et Descriptions**

**Formulaires avec labels appropriés :**
```typescript
// Composant Input accessible
interface AccessibleInputProps {
  label: string
  type?: string
  required?: boolean
  error?: string
  description?: string
  placeholder?: string
  value?: string
  onChange?: (value: string) => void
}

const AccessibleInput: React.FC<AccessibleInputProps> = ({
  label,
  type = 'text',
  required = false,
  error,
  description,
  placeholder,
  value,
  onChange
}) => {
  const inputId = useId()
  const errorId = useId()
  const descriptionId = useId()

  return (
    <div className="form-field">
      <label htmlFor={inputId} className="form-label">
        {label}
        {required && <span aria-label="requis"> *</span>}
      </label>
      
      {description && (
        <p id={descriptionId} className="form-description">
          {description}
        </p>
      )}
      
      <input
        id={inputId}
        type={type}
        placeholder={placeholder}
        value={value}
        onChange={(e) => onChange?.(e.target.value)}
        required={required}
        aria-invalid={!!error}
        aria-describedby={[
          description ? descriptionId : '',
          error ? errorId : ''
        ].filter(Boolean).join(' ') || undefined}
        className={cn(
          'form-input',
          error && 'form-input-error'
        )}
      />
      
      {error && (
        <p id={errorId} role="alert" className="form-error">
          {error}
        </p>
      )}
    </div>
  )
}
```

### **6.2 Groupes de Champs**

**Fieldsets et légendes :**
```typescript
// Formulaire avec groupes de champs
const UserProfileForm: React.FC = () => (
  <form>
    <fieldset>
      <legend>Informations personnelles</legend>
      <AccessibleInput
        label="Prénom"
        required
        description="Votre prénom tel qu'il apparaîtra sur votre profil"
      />
      <AccessibleInput
        label="Nom"
        required
        description="Votre nom de famille"
      />
    </fieldset>
    
    <fieldset>
      <legend>Préférences de contact</legend>
      <div role="group" aria-labelledby="contact-methods">
        <p id="contact-methods">Comment souhaitez-vous être contacté ?</p>
        <label>
          <input type="checkbox" name="contact" value="email" />
          Par email
        </label>
        <label>
          <input type="checkbox" name="contact" value="sms" />
          Par SMS
        </label>
      </div>
    </fieldset>
  </form>
)
```

---

## **7. TESTS MANUELS D'ACCESSIBILITÉ**

### **7.1 Checklist de Tests Manuels**

**Tests obligatoires à effectuer :**

**Navigation clavier :**
- [ ] Tous les éléments interactifs accessibles au clavier
- [ ] Ordre de tabulation logique
- [ ] Indicateurs de focus visibles
- [ ] Pas de piège à clavier
- [ ] Raccourcis clavier fonctionnels

**Lecteurs d'écran :**
- [ ] Contenu lu dans l'ordre logique
- [ ] Titres et structure correctement annoncés
- [ ] États des éléments communiqués
- [ ] Formulaires correctement étiquetés
- [ ] Images avec alt text approprié

**Zoom et redimensionnement :**
- [ ] Interface utilisable à 200% de zoom
- [ ] Pas de perte de fonctionnalité
- [ ] Texte reste lisible
- [ ] Pas de débordement horizontal

### **7.2 Tests avec Technologies d'Assistance**

**Outils de test recommandés :**
- **NVDA** (Windows) - Lecteur d'écran gratuit
- **JAWS** (Windows) - Lecteur d'écran professionnel
- **VoiceOver** (macOS) - Lecteur d'écran intégré
- **TalkBack** (Android) - Lecteur d'écran mobile
- **Dragon** - Reconnaissance vocale

**Scénarios de test :**
```typescript
// Tests manuels documentés
describe('Manual Accessibility Tests', () => {
  it('should be navigable with keyboard only', () => {
    // Instructions pour le testeur :
    // 1. Utiliser uniquement le clavier (Tab, Shift+Tab, Enter, Espace, flèches)
    // 2. Vérifier que tous les éléments interactifs sont accessibles
    // 3. Confirmer que l'ordre de navigation est logique
    // 4. S'assurer que le focus est toujours visible
  })

  it('should work with screen reader', () => {
    // Instructions pour le testeur :
    // 1. Activer NVDA/JAWS/VoiceOver
    // 2. Naviguer dans l'application
    // 3. Vérifier que le contenu est lu correctement
    // 4. Tester les formulaires et interactions
  })
})
```

---

## **8. DOCUMENTATION D'ACCESSIBILITÉ**

### **8.1 Déclaration d'Accessibilité**

**Template de déclaration :**
```markdown
# Déclaration d'Accessibilité

## Engagement d'accessibilité
Cette application s'engage à être accessible à tous les utilisateurs, 
conformément aux WCAG 2.1 niveau AA.

## État de conformité
- **Conforme** : Le contenu est conforme aux WCAG 2.1 AA
- **Partiellement conforme** : Certaines parties ne sont pas conformes
- **Non conforme** : Le contenu n'est pas conforme

## Contenu non accessible
[Liste des éléments non accessibles avec justifications]

## Retours et contact
Pour signaler un problème d'accessibilité :
- Email : <EMAIL>
- Téléphone : +33 1 23 45 67 89

## Date de la déclaration
Cette déclaration a été établie le [date] et mise à jour le [date].
```

### **8.2 Guide d'Utilisation**

**Documentation utilisateur :**
```markdown
# Guide d'Accessibilité pour les Utilisateurs

## Navigation au clavier
- **Tab** : Élément suivant
- **Shift + Tab** : Élément précédent
- **Enter** : Activer un lien ou bouton
- **Espace** : Cocher/décocher, activer un bouton
- **Flèches** : Navigation dans les menus et listes

## Raccourcis clavier
- **Alt + 1** : Aller au contenu principal
- **Alt + 2** : Aller au menu de navigation
- **Alt + 3** : Aller au pied de page
- **Ctrl + F** : Rechercher dans la page

## Paramètres d'accessibilité
L'application respecte les préférences système :
- Réduction des animations
- Contraste élevé
- Taille de police
```

---

**⚠️ CRITIQUE :** L'accessibilité est une obligation légale et éthique. Tous les composants et fonctionnalités DOIVENT être accessibles selon les standards WCAG 2.1 AA. Aucune exception n'est tolérée sans justification technique valide et solution alternative.

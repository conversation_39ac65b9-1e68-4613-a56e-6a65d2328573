Configuration de Firebase

**Objectif :** Ce document détaille les étapes pour configurer et intégrer Firebase (Authentication, Firestore, Storage) au projet.

**Directives pour l'agent de codage :**

*   **Documentation officielle Firebase :** En plus de la "Architecture de Projet & Guide pour Agent IA (v3)", la documentation officielle de Firebase est une source indispensable. Intégrez ses détails techniques pour une configuration robuste.
*   **Context7 pour des exemples précis :** Pour des cas d'utilisation spécifiques de Firebase (ex: configuration de règles Firestore, gestion des utilisateurs avec l'authentification, téléchargement de fichiers avec Storage), utilisez les serveurs MCP comme Context7 (`use context7`) pour des exemples de code fonctionnels et à jour. Context7 est particulièrement utile pour éviter les erreurs liées aux APIs obsolètes ou aux configurations génériques.
*   **Augment Code pour l'intégration :** Augment Code peut aider à écrire les hooks et services d'intégration Firebase, ainsi qu'à déboguer les appels API. Ses fonctions de Chat et Next Edit sont conçues pour accélérer ce type de développement.

Ce fichier guidera sur l'installation du SDK Firebase, l'initialisation de l'application Firebase, la configuration des services principaux et la gestion des identifiants d'environnement.

# **02_STRUCTURE-COMPOSANTS-REACT.MD - ARCHITECTURE DES COMPOSANTS**

## **OBJECTIF DE L'ARCHITECTURE COMPOSANTS**

Ce document définit l'organisation, la structure et les conventions pour tous les composants React du projet. Il établit les standards de développement qui garantissent la maintenabilité, la réutilisabilité et la cohérence du code.

---

## **1. PHILOSOPHIE D'ORGANISATION**

### **1.1 Principes Fondamentaux**

**Atomic Design Adapté :**
- **Atoms** : Composants de base non décomposables (Button, Input, Icon)
- **Molecules** : Combinaisons simples d'atoms (SearchBox, FormField)
- **Organisms** : Composants complexes métier (Header, ProductCard, UserProfile)
- **Templates** : Structures de page sans contenu spécifique
- **Pages** : Instances complètes avec contenu réel

**Séparation des Responsabilités :**
- **Composants de présentation** : Affichage uniquement, pas de logique métier
- **Composants conteneurs** : Gestion de l'état et logique métier
- **Hooks personnalisés** : Logique réutilisable extraite des composants

### **1.2 Structure des Dossiers**

**Organisation obligatoire dans `/src/components` :**
```
components/
├── ui/                 # Composants shadcn/ui et atoms personnalisés
│   ├── button.tsx
│   ├── input.tsx
│   ├── card.tsx
│   └── index.ts        # Exports centralisés
├── layout/             # Composants de mise en page
│   ├── Header/
│   │   ├── Header.tsx
│   │   ├── Header.test.tsx
│   │   └── index.ts
│   ├── Footer/
│   └── Sidebar/
├── features/           # Composants métier spécifiques
│   ├── auth/
│   │   ├── LoginForm/
│   │   ├── SignupForm/
│   │   └── index.ts
│   ├── dashboard/
│   └── profile/
└── common/             # Composants réutilisables transversaux
    ├── LoadingSpinner/
    ├── ErrorBoundary/
    ├── Modal/
    └── index.ts
```

---

## **2. CONVENTIONS DE NOMMAGE**

### **2.1 Noms de Composants**

**Règles obligatoires :**
- **PascalCase** pour tous les noms de composants
- **Noms descriptifs** et explicites
- **Préfixes** pour les composants spécialisés

**Exemples conformes :**
```typescript
// ✅ Correct
const UserProfileCard = () => { ... }
const ProductSearchForm = () => { ... }
const AdminDashboardHeader = () => { ... }

// ❌ Incorrect
const userCard = () => { ... }        // camelCase
const Card = () => { ... }            // Trop générique
const UPC = () => { ... }             // Abréviation
```

### **2.2 Noms de Fichiers**

**Convention obligatoire :**
- **PascalCase** pour les fichiers de composants
- **Dossier par composant** pour les composants complexes
- **index.ts** pour les exports

**Structure type :**
```
UserProfileCard/
├── UserProfileCard.tsx      # Composant principal
├── UserProfileCard.test.tsx # Tests unitaires
├── UserProfileCard.stories.tsx # Storybook (optionnel)
├── types.ts                 # Types spécifiques
└── index.ts                 # Export { UserProfileCard }
```

### **2.3 Props et Types**

**Conventions TypeScript :**
```typescript
// Interface des props avec suffixe Props
interface UserProfileCardProps {
  user: User
  onEdit?: () => void
  className?: string
  isLoading?: boolean
}

// Composant avec typage strict
const UserProfileCard: React.FC<UserProfileCardProps> = ({
  user,
  onEdit,
  className,
  isLoading = false
}) => {
  // Implémentation
}
```

---

## **3. STRUCTURE TYPE D'UN COMPOSANT**

### **3.1 Template de Composant Obligatoire**

**Fichier `ComponentName.tsx` :**
```typescript
import React from 'react'
import { cn } from '@/lib/utils'

// Types et interfaces
interface ComponentNameProps {
  // Props obligatoires
  title: string
  // Props optionnelles
  description?: string
  className?: string
  children?: React.ReactNode
  // Handlers
  onClick?: () => void
}

// Composant principal
const ComponentName: React.FC<ComponentNameProps> = ({
  title,
  description,
  className,
  children,
  onClick
}) => {
  // Hooks (état local, effets)
  const [isActive, setIsActive] = React.useState(false)

  // Handlers
  const handleClick = () => {
    setIsActive(!isActive)
    onClick?.()
  }

  // Render
  return (
    <div
      className={cn(
        "base-classes",
        isActive && "active-classes",
        className
      )}
      onClick={handleClick}
    >
      <h2>{title}</h2>
      {description && <p>{description}</p>}
      {children}
    </div>
  )
}

export default ComponentName
```

### **3.2 Fichier d'Export `index.ts`**

```typescript
export { default as ComponentName } from './ComponentName'
export type { ComponentNameProps } from './ComponentName'
```

### **3.3 Tests Unitaires `ComponentName.test.tsx`**

```typescript
import { render, screen, fireEvent } from '@testing-library/react'
import { ComponentName } from './ComponentName'

describe('ComponentName', () => {
  it('renders with required props', () => {
    render(<ComponentName title="Test Title" />)
    expect(screen.getByText('Test Title')).toBeInTheDocument()
  })

  it('handles click events', () => {
    const handleClick = vi.fn()
    render(<ComponentName title="Test" onClick={handleClick} />)

    fireEvent.click(screen.getByText('Test'))
    expect(handleClick).toHaveBeenCalledOnce()
  })
})
```

---

## **4. PATTERNS DE DÉVELOPPEMENT**

### **4.1 Composants de Présentation**

**Caractéristiques :**
- Pas d'état local complexe
- Props typées strictement
- Focalisés sur l'affichage
- Facilement testables

**Exemple :**
```typescript
interface ProductCardProps {
  product: Product
  onAddToCart: (productId: string) => void
  isInCart: boolean
}

const ProductCard: React.FC<ProductCardProps> = ({
  product,
  onAddToCart,
  isInCart
}) => (
  <Card className="product-card">
    <CardHeader>
      <CardTitle>{product.name}</CardTitle>
      <CardDescription>{product.price}€</CardDescription>
    </CardHeader>
    <CardContent>
      <img src={product.image} alt={product.name} />
    </CardContent>
    <CardFooter>
      <Button
        onClick={() => onAddToCart(product.id)}
        disabled={isInCart}
      >
        {isInCart ? 'Dans le panier' : 'Ajouter au panier'}
      </Button>
    </CardFooter>
  </Card>
)
```

### **4.2 Composants Conteneurs**

**Caractéristiques :**
- Gestion de l'état et de la logique métier
- Appels API et gestion des données
- Orchestration des composants de présentation

**Exemple :**
```typescript
const ProductListContainer: React.FC = () => {
  // État et logique métier
  const [products, setProducts] = useState<Product[]>([])
  const [cart, setCart] = useState<string[]>([])
  const [loading, setLoading] = useState(true)

  // Effets
  useEffect(() => {
    fetchProducts().then(setProducts).finally(() => setLoading(false))
  }, [])

  // Handlers
  const handleAddToCart = (productId: string) => {
    setCart(prev => [...prev, productId])
  }

  // Render avec composants de présentation
  if (loading) return <LoadingSpinner />

  return (
    <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
      {products.map(product => (
        <ProductCard
          key={product.id}
          product={product}
          onAddToCart={handleAddToCart}
          isInCart={cart.includes(product.id)}
        />
      ))}
    </div>
  )
}
```

### **4.3 Hooks Personnalisés**

**Extraction de la logique réutilisable :**
```typescript
// hooks/useCart.ts
export const useCart = () => {
  const [cart, setCart] = useState<string[]>([])

  const addToCart = useCallback((productId: string) => {
    setCart(prev => [...prev, productId])
  }, [])

  const removeFromCart = useCallback((productId: string) => {
    setCart(prev => prev.filter(id => id !== productId))
  }, [])

  const isInCart = useCallback((productId: string) => {
    return cart.includes(productId)
  }, [cart])

  return {
    cart,
    addToCart,
    removeFromCart,
    isInCart,
    cartCount: cart.length
  }
}
```

---

## **5. OPTIMISATION ET PERFORMANCE**

### **5.1 Mémorisation des Composants**

**React.memo pour éviter les re-rendus :**
```typescript
const ProductCard = React.memo<ProductCardProps>(({
  product,
  onAddToCart,
  isInCart
}) => {
  // Composant mémorisé
}, (prevProps, nextProps) => {
  // Comparaison personnalisée si nécessaire
  return prevProps.product.id === nextProps.product.id &&
         prevProps.isInCart === nextProps.isInCart
})
```

### **5.2 Optimisation des Callbacks**

**useCallback pour les handlers :**
```typescript
const ProductListContainer: React.FC = () => {
  const [cart, setCart] = useState<string[]>([])

  // Handler mémorisé
  const handleAddToCart = useCallback((productId: string) => {
    setCart(prev => [...prev, productId])
  }, [])

  // Vérification mémorisée
  const isInCart = useCallback((productId: string) => {
    return cart.includes(productId)
  }, [cart])

  return (
    // Utilisation des callbacks mémorisés
  )
}
```

### **5.3 Lazy Loading des Composants**

**Chargement différé pour les composants lourds :**
```typescript
// Lazy loading
const HeavyComponent = React.lazy(() => import('./HeavyComponent'))

// Utilisation avec Suspense
const App: React.FC = () => (
  <Suspense fallback={<LoadingSpinner />}>
    <HeavyComponent />
  </Suspense>
)
```

---

## **6. GESTION DES ERREURS**

### **6.1 Error Boundaries**

**Composant Error Boundary obligatoire :**
```typescript
interface ErrorBoundaryState {
  hasError: boolean
  error?: Error
}

class ErrorBoundary extends React.Component<
  React.PropsWithChildren<{}>,
  ErrorBoundaryState
> {
  constructor(props: React.PropsWithChildren<{}>) {
    super(props)
    this.state = { hasError: false }
  }

  static getDerivedStateFromError(error: Error): ErrorBoundaryState {
    return { hasError: true, error }
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    console.error('Error Boundary caught an error:', error, errorInfo)
  }

  render() {
    if (this.state.hasError) {
      return (
        <div className="error-boundary">
          <h2>Une erreur est survenue</h2>
          <p>Veuillez rafraîchir la page ou contacter le support.</p>
        </div>
      )
    }

    return this.props.children
  }
}
```

### **6.2 Gestion d'Erreurs dans les Composants**

**Pattern de gestion d'erreurs :**
```typescript
const DataComponent: React.FC = () => {
  const [data, setData] = useState(null)
  const [error, setError] = useState<string | null>(null)
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    fetchData()
      .then(setData)
      .catch(err => setError(err.message))
      .finally(() => setLoading(false))
  }, [])

  if (loading) return <LoadingSpinner />
  if (error) return <ErrorMessage message={error} />
  if (!data) return <EmptyState />

  return <DataDisplay data={data} />
}
```

---

## **7. VALIDATION ET STANDARDS**

### **7.1 Checklist de Validation Composant**

**Chaque composant DOIT respecter :**
- [ ] Nommage en PascalCase
- [ ] Props typées avec TypeScript
- [ ] Structure de dossier conforme
- [ ] Tests unitaires présents
- [ ] Gestion d'erreurs appropriée
- [ ] Optimisations de performance si nécessaire
- [ ] Documentation des props complexes
- [ ] Accessibilité (ARIA) si applicable

### **7.2 Review Checklist**

**Avant validation d'un composant :**
- [ ] Code lisible et bien structuré
- [ ] Respect des conventions de nommage
- [ ] Séparation appropriée des responsabilités
- [ ] Tests couvrant les cas principaux
- [ ] Performance optimisée
- [ ] Pas de code dupliqué
- [ ] Documentation suffisante

---

**⚠️ IMPORTANT :** Cette architecture de composants est la fondation de la maintenabilité du projet. Chaque composant doit respecter scrupuleusement ces standards pour garantir la cohérence et la qualité du code.

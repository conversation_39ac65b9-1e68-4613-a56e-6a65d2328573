# **08_CONFORMITE-RGPD.MD - CONFORMITÉ RÉGLEMENTAIRE**

## **OBJECTIF DE LA CONFORMITÉ RGPD**

Ce document définit les exigences strictes de conformité au Règlement Général sur la Protection des Données (RGPD) et autres réglementations de protection des données. La conformité n'est pas optionnelle et constitue un prérequis légal absolu pour toute application traitant des données personnelles.

---

## **1. PRINCIPES FONDAMENTAUX RGPD**

### **1.1 Principes Obligatoires**

**Les 7 principes du RGPD à respecter :**
1. **Licéité, loyauté, transparence** : Base légale claire pour chaque traitement
2. **Limitation des finalités** : Données collectées pour des fins déterminées
3. **Minimisation des données** : Collecte limitée au strict nécessaire
4. **Exactitude** : Données exactes et tenues à jour
5. **Limitation de conservation** : Durée de conservation définie
6. **Intégrité et confidentialité** : Sécurité appropriée des données
7. **Responsabilité** : Démonstration de la conformité

### **1.2 Classification des Données**

**Types de données personnelles :**
- **Données d'identification** : Nom, prénom, email, téléphone
- **Données techniques** : Adresse IP, cookies, identifiants
- **Données de navigation** : Pages visitées, temps de session
- **Données sensibles** : Santé, religion, opinions politiques (INTERDITES sauf exception)

**Traitement selon la classification :**
```typescript
// Classification des données dans l'application
enum DataCategory {
  IDENTIFICATION = 'identification',
  TECHNICAL = 'technical', 
  NAVIGATION = 'navigation',
  SENSITIVE = 'sensitive' // INTERDIT
}

interface PersonalData {
  category: DataCategory
  purpose: string[]
  legalBasis: LegalBasis
  retentionPeriod: number // en jours
  isRequired: boolean
}
```

---

## **2. GESTION DU CONSENTEMENT**

### **2.1 Mécanisme de Consentement**

**Service de gestion du consentement obligatoire :**
```typescript
// src/services/consent.ts
interface ConsentPreferences {
  necessary: boolean      // Toujours true (base légale : intérêt légitime)
  analytics: boolean      // Google Analytics
  marketing: boolean      // Campagnes marketing
  personalization: boolean // Personnalisation UX
}

interface ConsentRecord {
  version: string
  timestamp: Date
  preferences: ConsentPreferences
  ipAddress: string       // Anonymisée après 30 jours
  userAgent: string
  method: 'explicit' | 'implicit' | 'updated'
}

class ConsentService {
  private static readonly CONSENT_KEY = 'user_consent'
  private static readonly CONSENT_VERSION = '2.0'
  private static readonly CONSENT_DURATION = 13 * 30 * 24 * 60 * 60 * 1000 // 13 mois

  // Vérifier si le consentement est requis
  static isConsentRequired(): boolean {
    const stored = this.getStoredConsent()
    if (!stored) return true
    
    // Vérifier l'expiration
    const expirationDate = new Date(stored.timestamp.getTime() + this.CONSENT_DURATION)
    if (new Date() > expirationDate) return true
    
    // Vérifier la version
    return stored.version !== this.CONSENT_VERSION
  }

  // Obtenir le consentement stocké
  static getStoredConsent(): ConsentRecord | null {
    try {
      const stored = localStorage.getItem(this.CONSENT_KEY)
      if (!stored) return null
      
      const parsed = JSON.parse(stored)
      return {
        ...parsed,
        timestamp: new Date(parsed.timestamp)
      }
    } catch {
      return null
    }
  }

  // Enregistrer le consentement
  static setConsent(preferences: ConsentPreferences, method: 'explicit' | 'implicit' = 'explicit'): void {
    const consentRecord: ConsentRecord = {
      version: this.CONSENT_VERSION,
      timestamp: new Date(),
      preferences,
      ipAddress: this.getAnonymizedIP(),
      userAgent: navigator.userAgent,
      method
    }

    // Stocker localement
    localStorage.setItem(this.CONSENT_KEY, JSON.stringify(consentRecord))
    
    // Envoyer au serveur pour audit
    this.sendConsentToServer(consentRecord)
    
    // Appliquer les préférences
    this.applyConsentPreferences(preferences)
    
    // Log pour audit
    console.log('Consent recorded:', {
      version: consentRecord.version,
      timestamp: consentRecord.timestamp,
      preferences: consentRecord.preferences,
      method: consentRecord.method
    })
  }

  // Retirer le consentement
  static withdrawConsent(): void {
    const currentConsent = this.getStoredConsent()
    if (!currentConsent) return

    // Consentement minimal (nécessaire uniquement)
    const minimalPreferences: ConsentPreferences = {
      necessary: true,
      analytics: false,
      marketing: false,
      personalization: false
    }

    this.setConsent(minimalPreferences, 'explicit')
    
    // Nettoyer les données existantes
    this.cleanupUserData()
  }

  // Appliquer les préférences de consentement
  private static applyConsentPreferences(preferences: ConsentPreferences): void {
    // Analytics
    if (preferences.analytics) {
      // Activer Google Analytics
      window.gtag?.('consent', 'update', {
        analytics_storage: 'granted'
      })
    } else {
      // Désactiver Google Analytics
      window.gtag?.('consent', 'update', {
        analytics_storage: 'denied'
      })
    }

    // Marketing
    if (preferences.marketing) {
      window.gtag?.('consent', 'update', {
        ad_storage: 'granted'
      })
    } else {
      window.gtag?.('consent', 'update', {
        ad_storage: 'denied'
      })
    }

    // Personnalisation
    if (!preferences.personalization) {
      // Supprimer les cookies de personnalisation
      this.clearPersonalizationCookies()
    }
  }

  // Anonymiser l'adresse IP
  private static getAnonymizedIP(): string {
    // En production, récupérer l'IP côté serveur et l'anonymiser
    return 'anonymized'
  }

  // Envoyer le consentement au serveur
  private static async sendConsentToServer(consent: ConsentRecord): Promise<void> {
    try {
      await fetch('/api/consent', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(consent)
      })
    } catch (error) {
      console.error('Failed to send consent to server:', error)
    }
  }

  // Nettoyer les données utilisateur
  private static cleanupUserData(): void {
    // Supprimer les cookies non nécessaires
    this.clearAnalyticsCookies()
    this.clearMarketingCookies()
    this.clearPersonalizationCookies()
    
    // Vider les caches locaux
    sessionStorage.clear()
    
    // Garder uniquement les données nécessaires dans localStorage
    const necessaryKeys = [this.CONSENT_KEY, 'sessionId']
    const allKeys = Object.keys(localStorage)
    allKeys.forEach(key => {
      if (!necessaryKeys.includes(key)) {
        localStorage.removeItem(key)
      }
    })
  }

  private static clearAnalyticsCookies(): void {
    const analyticsCookies = ['_ga', '_ga_', '_gid', '_gat']
    analyticsCookies.forEach(cookie => {
      document.cookie = `${cookie}=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;`
    })
  }

  private static clearMarketingCookies(): void {
    const marketingCookies = ['_fbp', '_fbc', '__utm']
    marketingCookies.forEach(cookie => {
      document.cookie = `${cookie}=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;`
    })
  }

  private static clearPersonalizationCookies(): void {
    const personalizationCookies = ['user_preferences', 'theme', 'language']
    personalizationCookies.forEach(cookie => {
      document.cookie = `${cookie}=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;`
    })
  }
}

export { ConsentService, type ConsentPreferences, type ConsentRecord }
```

### **2.2 Interface de Consentement**

**Composant de bannière de consentement :**
```typescript
// src/components/common/ConsentBanner.tsx
import React, { useState, useEffect } from 'react'
import { ConsentService, ConsentPreferences } from '@/services/consent'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardFooter, CardHeader, CardTitle } from '@/components/ui/card'
import { Switch } from '@/components/ui/switch'

const ConsentBanner: React.FC = () => {
  const [showBanner, setShowBanner] = useState(false)
  const [showDetails, setShowDetails] = useState(false)
  const [preferences, setPreferences] = useState<ConsentPreferences>({
    necessary: true,
    analytics: false,
    marketing: false,
    personalization: false
  })

  useEffect(() => {
    setShowBanner(ConsentService.isConsentRequired())
  }, [])

  const handleAcceptAll = () => {
    const allAccepted: ConsentPreferences = {
      necessary: true,
      analytics: true,
      marketing: true,
      personalization: true
    }
    ConsentService.setConsent(allAccepted, 'explicit')
    setShowBanner(false)
  }

  const handleRejectAll = () => {
    const minimal: ConsentPreferences = {
      necessary: true,
      analytics: false,
      marketing: false,
      personalization: false
    }
    ConsentService.setConsent(minimal, 'explicit')
    setShowBanner(false)
  }

  const handleSavePreferences = () => {
    ConsentService.setConsent(preferences, 'explicit')
    setShowBanner(false)
  }

  if (!showBanner) return null

  return (
    <div className="fixed bottom-0 left-0 right-0 z-50 p-4 bg-background/95 backdrop-blur border-t">
      <Card className="max-w-4xl mx-auto">
        <CardHeader>
          <CardTitle>Respect de votre vie privée</CardTitle>
        </CardHeader>
        <CardContent>
          <p className="text-sm text-muted-foreground mb-4">
            Nous utilisons des cookies et technologies similaires pour améliorer votre expérience, 
            analyser notre trafic et personnaliser le contenu. Vous pouvez choisir vos préférences ci-dessous.
          </p>
          
          {showDetails && (
            <div className="space-y-4 mb-4">
              <div className="flex items-center justify-between">
                <div>
                  <h4 className="font-medium">Cookies nécessaires</h4>
                  <p className="text-xs text-muted-foreground">
                    Requis pour le fonctionnement du site (authentification, sécurité)
                  </p>
                </div>
                <Switch checked={true} disabled />
              </div>
              
              <div className="flex items-center justify-between">
                <div>
                  <h4 className="font-medium">Cookies d'analyse</h4>
                  <p className="text-xs text-muted-foreground">
                    Nous aident à comprendre comment vous utilisez notre site
                  </p>
                </div>
                <Switch 
                  checked={preferences.analytics}
                  onCheckedChange={(checked) => 
                    setPreferences(prev => ({ ...prev, analytics: checked }))
                  }
                />
              </div>
              
              <div className="flex items-center justify-between">
                <div>
                  <h4 className="font-medium">Cookies marketing</h4>
                  <p className="text-xs text-muted-foreground">
                    Utilisés pour vous proposer des publicités pertinentes
                  </p>
                </div>
                <Switch 
                  checked={preferences.marketing}
                  onCheckedChange={(checked) => 
                    setPreferences(prev => ({ ...prev, marketing: checked }))
                  }
                />
              </div>
              
              <div className="flex items-center justify-between">
                <div>
                  <h4 className="font-medium">Cookies de personnalisation</h4>
                  <p className="text-xs text-muted-foreground">
                    Permettent de mémoriser vos préférences (thème, langue)
                  </p>
                </div>
                <Switch 
                  checked={preferences.personalization}
                  onCheckedChange={(checked) => 
                    setPreferences(prev => ({ ...prev, personalization: checked }))
                  }
                />
              </div>
            </div>
          )}
        </CardContent>
        <CardFooter className="flex flex-wrap gap-2">
          <Button onClick={handleAcceptAll}>
            Tout accepter
          </Button>
          <Button variant="outline" onClick={handleRejectAll}>
            Tout refuser
          </Button>
          <Button 
            variant="ghost" 
            onClick={() => setShowDetails(!showDetails)}
          >
            {showDetails ? 'Masquer les détails' : 'Personnaliser'}
          </Button>
          {showDetails && (
            <Button onClick={handleSavePreferences}>
              Enregistrer mes préférences
            </Button>
          )}
          <Button variant="link" size="sm">
            <a href="/privacy-policy" target="_blank">
              Politique de confidentialité
            </a>
          </Button>
        </CardFooter>
      </Card>
    </div>
  )
}

export default ConsentBanner
```

---

## **3. DROIT À L'OUBLI**

### **3.1 Implémentation du Droit à l'Effacement**

**Service de suppression des données :**
```typescript
// src/services/dataErasure.ts
interface ErasureRequest {
  userId: string
  requestDate: Date
  reason: 'withdrawal' | 'objection' | 'unlawful' | 'no_longer_necessary'
  status: 'pending' | 'processing' | 'completed' | 'rejected'
  completionDate?: Date
  rejectionReason?: string
}

class DataErasureService {
  // Demander l'effacement des données
  static async requestDataErasure(userId: string, reason: ErasureRequest['reason']): Promise<string> {
    const requestId = crypto.randomUUID()
    
    const erasureRequest: ErasureRequest = {
      userId,
      requestDate: new Date(),
      reason,
      status: 'pending'
    }

    try {
      // Envoyer la demande au serveur
      const response = await fetch('/api/data-erasure', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${await this.getAuthToken()}`
        },
        body: JSON.stringify({ requestId, ...erasureRequest })
      })

      if (!response.ok) {
        throw new Error('Failed to submit erasure request')
      }

      // Log de la demande
      console.log('Data erasure requested:', {
        requestId,
        userId,
        reason,
        timestamp: erasureRequest.requestDate
      })

      return requestId
    } catch (error) {
      console.error('Error requesting data erasure:', error)
      throw error
    }
  }

  // Vérifier le statut d'une demande
  static async getErasureStatus(requestId: string): Promise<ErasureRequest> {
    const response = await fetch(`/api/data-erasure/${requestId}`, {
      headers: {
        'Authorization': `Bearer ${await this.getAuthToken()}`
      }
    })

    if (!response.ok) {
      throw new Error('Failed to get erasure status')
    }

    return response.json()
  }

  // Effacement immédiat côté client (en attendant le traitement serveur)
  static immediateClientErasure(): void {
    // Supprimer toutes les données locales
    localStorage.clear()
    sessionStorage.clear()
    
    // Supprimer tous les cookies
    document.cookie.split(";").forEach(cookie => {
      const eqPos = cookie.indexOf("=")
      const name = eqPos > -1 ? cookie.substr(0, eqPos) : cookie
      document.cookie = `${name}=;expires=Thu, 01 Jan 1970 00:00:00 GMT;path=/`
    })
    
    // Vider les caches
    if ('caches' in window) {
      caches.keys().then(names => {
        names.forEach(name => caches.delete(name))
      })
    }
    
    // Rediriger vers une page de confirmation
    window.location.href = '/data-erased'
  }

  private static async getAuthToken(): Promise<string> {
    // Récupérer le token d'authentification
    // À implémenter selon votre système d'auth
    return 'auth-token'
  }
}

export { DataErasureService, type ErasureRequest }
```

---

## **4. ANONYMISATION DES DONNÉES**

### **4.1 Service d'Anonymisation**

**Techniques d'anonymisation obligatoires :**
```typescript
// src/services/anonymization.ts
import { createHash } from 'crypto'

class AnonymizationService {
  private static readonly SALT = process.env.VITE_ANONYMIZATION_SALT || 'default-salt'

  // Anonymiser une adresse email
  static anonymizeEmail(email: string): string {
    const [localPart, domain] = email.split('@')
    const hashedLocal = this.hashString(localPart)
    return `${hashedLocal.substring(0, 8)}@${domain}`
  }

  // Anonymiser une adresse IP
  static anonymizeIP(ip: string): string {
    const parts = ip.split('.')
    if (parts.length === 4) {
      // IPv4: masquer le dernier octet
      return `${parts[0]}.${parts[1]}.${parts[2]}.0`
    }
    // IPv6: masquer les 4 derniers groupes
    const ipv6Parts = ip.split(':')
    return ipv6Parts.slice(0, 4).join(':') + '::0'
  }

  // Anonymiser un nom
  static anonymizeName(name: string): string {
    if (name.length <= 2) return '**'
    return name.charAt(0) + '*'.repeat(name.length - 2) + name.charAt(name.length - 1)
  }

  // Hash irréversible d'une chaîne
  static hashString(input: string): string {
    return createHash('sha256')
      .update(input + this.SALT)
      .digest('hex')
  }

  // Anonymiser un objet utilisateur complet
  static anonymizeUserData(userData: any): any {
    return {
      ...userData,
      id: this.hashString(userData.id),
      email: userData.email ? this.anonymizeEmail(userData.email) : null,
      name: userData.name ? this.anonymizeName(userData.name) : null,
      firstName: userData.firstName ? this.anonymizeName(userData.firstName) : null,
      lastName: userData.lastName ? this.anonymizeName(userData.lastName) : null,
      phone: userData.phone ? this.anonymizePhone(userData.phone) : null,
      ipAddress: userData.ipAddress ? this.anonymizeIP(userData.ipAddress) : null,
      // Supprimer les données sensibles
      password: undefined,
      socialSecurityNumber: undefined,
      creditCard: undefined
    }
  }

  // Anonymiser un numéro de téléphone
  private static anonymizePhone(phone: string): string {
    const digits = phone.replace(/\D/g, '')
    if (digits.length < 4) return '****'
    return digits.substring(0, 2) + '*'.repeat(digits.length - 4) + digits.substring(digits.length - 2)
  }

  // Vérifier si des données sont anonymisées
  static isAnonymized(data: any): boolean {
    const sensitiveFields = ['email', 'phone', 'name', 'firstName', 'lastName']
    return sensitiveFields.every(field => {
      if (!data[field]) return true
      return data[field].includes('*') || data[field].includes('anonymized')
    })
  }
}

export { AnonymizationService }
```

---

## **5. DOCUMENTATION DES TRAITEMENTS**

### **5.1 Registre des Traitements**

**Documentation obligatoire des traitements :**
```typescript
// src/config/dataProcessing.ts
interface DataProcessing {
  id: string
  name: string
  purpose: string[]
  legalBasis: 'consent' | 'contract' | 'legal_obligation' | 'vital_interests' | 'public_task' | 'legitimate_interests'
  dataCategories: string[]
  dataSubjects: string[]
  recipients: string[]
  retentionPeriod: number // en jours
  securityMeasures: string[]
  transfersOutsideEU: boolean
  automatedDecisionMaking: boolean
}

export const DATA_PROCESSING_REGISTRY: DataProcessing[] = [
  {
    id: 'user-authentication',
    name: 'Authentification des utilisateurs',
    purpose: ['Authentification', 'Sécurité du compte'],
    legalBasis: 'contract',
    dataCategories: ['Email', 'Mot de passe haché', 'Date de création'],
    dataSubjects: ['Utilisateurs inscrits'],
    recipients: ['Firebase Authentication'],
    retentionPeriod: 1095, // 3 ans
    securityMeasures: ['Chiffrement', 'Hachage des mots de passe', 'HTTPS'],
    transfersOutsideEU: true, // Firebase US
    automatedDecisionMaking: false
  },
  {
    id: 'analytics',
    name: 'Analyse d\'audience',
    purpose: ['Amélioration du service', 'Statistiques d\'usage'],
    legalBasis: 'consent',
    dataCategories: ['Adresse IP anonymisée', 'Pages visitées', 'Durée de session'],
    dataSubjects: ['Visiteurs du site'],
    recipients: ['Google Analytics'],
    retentionPeriod: 730, // 2 ans
    securityMeasures: ['Anonymisation IP', 'Chiffrement en transit'],
    transfersOutsideEU: true, // Google US
    automatedDecisionMaking: false
  },
  {
    id: 'user-preferences',
    name: 'Préférences utilisateur',
    purpose: ['Personnalisation de l\'expérience'],
    legalBasis: 'legitimate_interests',
    dataCategories: ['Préférences d\'affichage', 'Langue', 'Thème'],
    dataSubjects: ['Utilisateurs'],
    recipients: ['Stockage local navigateur'],
    retentionPeriod: 365, // 1 an
    securityMeasures: ['Stockage local sécurisé'],
    transfersOutsideEU: false,
    automatedDecisionMaking: false
  }
]
```

---

**⚠️ CRITIQUE :** La conformité RGPD est une obligation légale absolue. Tous les mécanismes définis dans ce document DOIVENT être implémentés avant la mise en production. Le non-respect du RGPD expose à des amendes pouvant atteindre 4% du chiffre d'affaires annuel ou 20 millions d'euros.

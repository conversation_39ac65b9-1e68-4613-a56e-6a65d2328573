# 04_tests-unitaire-integration.md : Tests Unitaires et d'Intégration

**Objectif :** Ce document fournit des directives pour écrire des tests unitaires et d'intégration afin d'assurer la qualité et la robustesse du code.

**Directives pour l'agent de codage :**

1.  **Philosophie de test :** Adoptez une approche "Test-Driven Development (TDD)" ou "Test-First" si possible, en écrivant les tests avant ou pendant le développement du code. L'objectif est de garantir que chaque unité de code fonctionne comme prévu et que les interactions entre les unités sont correctes.
2.  **Choix de la bibliothèque de test :** Utilisez **Jest** pour les tests unitaires et **React Testing Library** (RTL) pour les tests de composants React et les tests d'intégration, car RTL encourage des tests axés sur le comportement utilisateur.
3.  **Tests unitaires :**
    *   Chaque fonction pure, classe ou module logique doit avoir ses propres tests unitaires dédiés.
    *   Mockez les dépendances externes pour isoler l'unité testée.
    *   Assurez-vous que les tests couvrent les cas nominaux, les cas limites et les chemins d'erreur.
4.  **Tests de composants (avec RTL) :**
    *   Testez les composants React du point de vue de l'utilisateur : comment il interagit avec le composant (clics, saisies) et quel est le rendu visuel et le comportement attendu.
    *   Utilisez les requêtes (`getByRole`, `getByText`, etc.) de RTL pour interagir avec le DOM rendu par le composant.
    *   Vérifiez l'affichage correct des éléments, les interactions utilisateur, la mise à jour de l'état et le déclenchement des événements.
5.  **Tests d'intégration :**
    *   Testez les interactions entre plusieurs composants ou entre des composants et des services externes (ex: Firebase).
    *   Pour les interactions Firebase, envisagez de mocker le SDK Firebase ou d'utiliser un émulateur Firebase pour des tests plus rapides et reproductibles, sans impacter les bases de données réelles.
    *   Context7 (`use context7`) peut fournir des exemples sur la manière de mocker des modules ou des APIs spécifiques pour les tests.
6.  **Couverture de code :** Visez une couverture de code raisonnable, mais priorisez la qualité des tests sur une simple métrique de pourcentage. Un test qui vérifie le comportement attendu est plus précieux qu'un test qui ne fait qu'augmenter la couverture sans réelle valeur.
7.  **Organisation des tests :** Les fichiers de test doivent résider aux côtés du code qu'ils testent (ex: `MyComponent.test.tsx` dans le dossier de `MyComponent`).
8.  **Documentation des tests :** Écrivez des commentaires dans les tests pour expliquer les raisons de chaque test et comment ils sont écrits. Cela facilitera la maintenance et la compréhension des tests
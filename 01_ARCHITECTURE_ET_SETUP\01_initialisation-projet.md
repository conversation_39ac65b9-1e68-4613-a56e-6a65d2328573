# **01_INITIALISATION-PROJET.MD - CRÉATION ET CONFIGURATION INITIALE**

## **OBJECTIF DE L'INITIALISATION**

Ce document guide l'agent dans l'initialisation complète d'un nouveau projet React/TypeScript ou l'audit d'un projet existant, en respectant scrupuleusement la stack technologique autorisée et les standards de qualité définis.

---

## **1. PROCESSUS D'INITIALISATION NOUVEAU PROJET**

### **1.1 Vérifications Préalables Obligatoires**

**AVANT toute action technique, l'agent DOIT :**
1. **Confirmer** que la phase de briefing (dossier 00_) est complètement terminée
2. **Vérifier** que Cisco a validé explicitement le cahier des charges
3. **S'assurer** que toutes les spécifications techniques sont claires
4. **Obtenir** l'approbation pour procéder à l'initialisation

### **1.2 Création du Projet Vite + React + TypeScript**

**Commandes d'initialisation obligatoires :**
```bash
# Création du projet avec template React + TypeScript
npm create vite@latest [nom-du-projet] -- --template react-ts

# Navigation dans le dossier
cd [nom-du-projet]

# Installation des dépendances de base
npm install

# Test de fonctionnement
npm run dev
```

**Vérification post-création :**
- [ ] Le serveur de développement démarre sur le port 3000
- [ ] La page d'accueil Vite s'affiche correctement
- [ ] Aucune erreur dans la console du navigateur
- [ ] Aucune erreur dans le terminal

### **1.3 Structure de Dossiers Obligatoire**

**Architecture à créer dans `/src` :**
```
src/
├── components/          # Composants React
│   ├── ui/             # Composants shadcn/ui
│   ├── layout/         # Composants de mise en page
│   ├── features/       # Composants métier spécifiques
│   └── common/         # Composants réutilisables
├── pages/              # Pages de l'application
├── hooks/              # Hooks personnalisés
├── utils/              # Fonctions utilitaires
├── types/              # Types TypeScript
├── services/           # Services API et Firebase
├── assets/             # Ressources statiques
│   ├── images/         # Images
│   ├── icons/          # Icônes
│   └── fonts/          # Polices personnalisées
├── styles/             # Styles globaux
└── test/               # Configuration et utilitaires de test
```

**Commandes de création :**
```bash
# Création de la structure de dossiers
mkdir -p src/{components/{ui,layout,features,common},pages,hooks,utils,types,services,assets/{images,icons,fonts},styles,test}

# Création des fichiers index.ts pour les exports
touch src/components/index.ts
touch src/hooks/index.ts
touch src/utils/index.ts
touch src/types/index.ts
touch src/services/index.ts
```

---

## **2. INSTALLATION DES DÉPENDANCES OBLIGATOIRES**

### **2.1 Dépendances de Production**

**Installation des packages essentiels :**
```bash
# Routing
npm install react-router-dom

# State Management (si nécessaire)
npm install zustand

# Formulaires et validation
npm install react-hook-form @hookform/resolvers zod

# Firebase
npm install firebase

# Animations GSAP
npm install gsap

# Three.js (si nécessaire pour le projet)
npm install three @react-three/fiber @react-three/drei

# Utilitaires
npm install clsx tailwind-merge
npm install date-fns
npm install uuid

# Types pour les packages
npm install -D @types/uuid
npm install -D @types/three
```

### **2.2 Configuration Tailwind CSS + shadcn/ui**

**Installation Tailwind CSS :**
```bash
npm install -D tailwindcss postcss autoprefixer
npx tailwindcss init -p
```

**Installation shadcn/ui :**
```bash
npx shadcn-ui@latest init
```

**Configuration lors de l'init shadcn/ui :**
- TypeScript: Yes
- Style: Default
- Base color: Slate
- Global CSS file: src/styles/globals.css
- CSS variables: Yes
- Tailwind config: tailwind.config.js
- Components: src/components
- Utils: src/lib/utils.ts

### **2.3 Dépendances de Développement**

**Outils de développement obligatoires :**
```bash
# Linting et formatting
npm install -D eslint @typescript-eslint/eslint-plugin @typescript-eslint/parser
npm install -D eslint-plugin-react eslint-plugin-react-hooks eslint-plugin-react-refresh
npm install -D eslint-plugin-jsx-a11y
npm install -D prettier eslint-config-prettier eslint-plugin-prettier

# Testing
npm install -D vitest @testing-library/react @testing-library/jest-dom @testing-library/user-event jsdom

# Git hooks
npm install -D husky lint-staged

# Analyse des bundles
npm install -D vite-bundle-analyzer
```

---

## **3. CONFIGURATION INITIALE**

### **3.1 Configuration des Fichiers de Base**

**L'agent DOIT créer/configurer :**
1. **`vite.config.ts`** : Configuration Vite avec alias et optimisations
2. **`tsconfig.json`** : Configuration TypeScript stricte
3. **`.eslintrc.cjs`** : Configuration ESLint avec règles strictes
4. **`.prettierrc`** : Configuration Prettier
5. **`.gitignore`** : Exclusions Git appropriées
6. **`tailwind.config.js`** : Configuration Tailwind + shadcn/ui

**Référence :** Voir le fichier `06_configuration-outils-dev.md` pour les configurations détaillées.

### **3.2 Fichiers d'Environnement**

**Création du fichier `.env.local` :**
```env
# Configuration Firebase (à compléter avec les vraies valeurs)
VITE_FIREBASE_API_KEY=your_api_key_here
VITE_FIREBASE_AUTH_DOMAIN=your_project.firebaseapp.com
VITE_FIREBASE_PROJECT_ID=your_project_id
VITE_FIREBASE_STORAGE_BUCKET=your_project.appspot.com
VITE_FIREBASE_MESSAGING_SENDER_ID=123456789
VITE_FIREBASE_APP_ID=1:123456789:web:abcdef

# Variables de développement
VITE_APP_ENV=development
VITE_APP_VERSION=1.0.0
```

**⚠️ CRITIQUE :** Vérifier que `.env.local` est bien dans `.gitignore`

### **3.3 Configuration Git et Hooks**

**Initialisation Git :**
```bash
git init
git add .
git commit -m "feat: initialisation du projet avec stack React/TypeScript/Vite"

# Configuration Husky
npx husky install
npx husky add .husky/pre-commit "npx lint-staged"
```

---

## **4. PROCESSUS D'AUDIT PROJET EXISTANT**

### **4.1 Analyse de Conformité**

**Si un projet existe déjà, l'agent DOIT :**

**Étape 1 - Audit de la structure :**
- [ ] Vérifier la présence de la stack autorisée (React 18+, TypeScript, Vite)
- [ ] Analyser l'organisation des dossiers vs structure recommandée
- [ ] Identifier les technologies non autorisées
- [ ] Évaluer la qualité de l'architecture existante

**Étape 2 - Audit de sécurité :**
- [ ] Vérifier la présence et configuration de `.env.local`
- [ ] Contrôler l'intégrité du `.gitignore`
- [ ] Examiner les dépendances pour les vulnérabilités (`npm audit`)
- [ ] Vérifier les règles de sécurité Firebase si présentes

**Étape 3 - Audit de qualité :**
- [ ] Analyser la configuration ESLint/Prettier
- [ ] Vérifier la présence de tests
- [ ] Évaluer la qualité du code TypeScript (strict mode, types)
- [ ] Identifier les "code smells" et anti-patterns

### **4.2 Rapport d'Audit Obligatoire**

**L'agent DOIT produire un rapport structuré :**

```markdown
# RAPPORT D'AUDIT - [Nom du projet]

## Conformité Stack Technologique
- ✅/❌ React 18+ : [Version détectée]
- ✅/❌ TypeScript : [Configuration stricte]
- ✅/❌ Vite.js : [Version et configuration]
- ✅/❌ Tailwind CSS : [Présence et configuration]

## Architecture et Organisation
- Structure des dossiers : [Conforme/Non conforme]
- Organisation des composants : [Évaluation]
- Gestion des états : [Solution utilisée]
- Routing : [Solution et configuration]

## Sécurité
- Variables d'environnement : [Sécurisées/À corriger]
- Dépendances : [X vulnérabilités détectées]
- Configuration Firebase : [Sécurisée/À revoir]

## Qualité du Code
- Configuration ESLint : [Présente/Absente/À améliorer]
- Configuration Prettier : [Présente/Absente]
- Tests : [Couverture X%/Absents]
- Documentation : [Suffisante/Insuffisante]

## Recommandations Prioritaires
1. [Action prioritaire 1]
2. [Action prioritaire 2]
3. [Action prioritaire 3]

## Plan de Mise en Conformité
- Phase 1 : [Actions critiques]
- Phase 2 : [Améliorations importantes]
- Phase 3 : [Optimisations]
```

---

## **5. VALIDATION ET TRANSITION**

### **5.1 Tests de Validation Post-Initialisation**

**Vérifications obligatoires :**
```bash
# Vérification TypeScript
npm run type-check

# Vérification ESLint
npm run lint

# Vérification Prettier
npm run format:check

# Build de production
npm run build

# Tests (si configurés)
npm run test
```

### **5.2 Checklist de Fin d'Initialisation**

**Projet nouveau :**
- [ ] Structure de dossiers créée et conforme
- [ ] Toutes les dépendances installées et fonctionnelles
- [ ] Configuration des outils de développement complète
- [ ] Variables d'environnement configurées
- [ ] Git initialisé avec hooks fonctionnels
- [ ] Build de production réussie
- [ ] Documentation technique de base créée

**Projet existant :**
- [ ] Audit complet réalisé et documenté
- [ ] Rapport de conformité produit
- [ ] Plan de mise en conformité établi
- [ ] Approbation de Cisco obtenue pour les modifications

### **5.3 Transition vers la Phase Suivante**

**Conditions pour passer au dossier `02_DEVELOPPEMENT` :**
- ✅ Initialisation/audit complètement terminé
- ✅ Tous les outils de développement fonctionnels
- ✅ Structure de projet conforme aux standards
- ✅ Aucune erreur bloquante détectée
- ✅ Approbation explicite de Cisco obtenue

---

**⚠️ RAPPEL CRITIQUE :** Cette phase d'initialisation conditionne la réussite de tout le projet. L'agent ne doit JAMAIS précipiter cette étape et doit s'assurer que tous les fondements techniques sont solides avant de passer au développement.
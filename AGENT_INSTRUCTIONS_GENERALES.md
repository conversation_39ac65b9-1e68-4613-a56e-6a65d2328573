# **FRAMEWORK D'INSTRUCTIONS POUR AGENTS DE DÉVELOPPEMENT IA - VERSION 5.0**

## **DOCUMENT MAÎTRE - ARCHITECTURE ET DIRECTIVES GÉNÉRALES**

Ce document constitue la référence absolue pour tous les agents de développement IA travaillant sur des projets web React/TypeScript. Chaque instruction doit être suivie scrupuleusement, sans exception ni interprétation personnelle.

---

## **1. STRUCTURE ORGANISATIONNELLE DU FRAMEWORK**

### **Architecture des Dossiers d'Instructions**

```
/FRAMEWORK_INSTRUCTIONS/
│
├── 00_BRIEFING_ET_OBJECTIFS/           # Phase de collecte et analyse
│   ├── 00_prompt-initial.md            # Briefing initial du projet
│   ├── 01_cahier-des-charges.md        # Spécifications techniques détaillées
│   ├── 02_user-stories.md              # Scénarios utilisateur et fonctionnalités
│   ├── 03_maquettes-et-inspiration.md  # Références visuelles et UX
│   └── 04_analyse-technique.md         # Analyse de faisabilité technique
│
├── 01_ARCHITECTURE_ET_SETUP/           # Phase d'initialisation technique
│   ├── 01_initialisation-projet.md     # Création et configuration initiale
│   ├── 02_structure-composants-react.md # Architecture des composants
│   ├── 03_configuration-tailwind.md    # Setup et personnalisation CSS
│   ├── 04_setup-firebase.md            # Configuration backend Firebase
│   ├── 05_gestion-environnement-env.md # Variables d'environnement
│   └── 06_configuration-outils-dev.md  # Outils de développement
│
├── 02_DEVELOPPEMENT/                   # Phase de développement
│   ├── 01_directives-codage.md         # Standards et conventions de code
│   ├── 02_developpement-composants-react.md # Implémentation composants
│   ├── 03_interaction-firebase.md      # Intégration backend
│   ├── 04_tests-unitaire-integration.md # Stratégies de test
│   ├── 05_debogage-optimisation.md     # Debug et performance
│   ├── 06_gestion-erreurs.md           # Gestion d'erreurs robuste
│   └── 07_securite-donnees.md          # Sécurité et protection des données
│
├── 03_QUALITE_ET_TESTS/               # Phase de validation
│   ├── 01_checklist-qualite.md        # Contrôles qualité obligatoires
│   ├── 02_scenarios-de-test-manuel.md # Tests manuels systématiques
│   ├── 03_rapports-de-bugs.md         # Documentation des problèmes
│   ├── 04_tests-automatises.md        # Tests automatisés
│   └── 05_audit-accessibilite.md      # Conformité accessibilité
│
├── 04_DEPLOIEMENT_ET_MAINTENANCE/     # Phase de mise en production
│   ├── 01_checklist-deploiement-netlify.md # Déploiement Netlify
│   ├── 02_checklist-deploiement-firebase.md # Déploiement Firebase
│   ├── 03_monitoring-et-logs.md       # Surveillance et logs
│   ├── 04_maintenance-preventive.md   # Maintenance et mises à jour
│   └── 05_procedures-urgence.md       # Gestion des incidents
│
├── 99_ARCHIVES/                       # Historique et documentation
│   ├── journal-des-changements.md     # Historique des modifications
│   └── versions-precedentes/          # Archives des versions
│
└── AGENT_INSTRUCTIONS_GENERALES.md    # CE FICHIER - Document maître
```

### **Principe de Navigation Séquentielle**

**RÈGLE ABSOLUE :** Les dossiers doivent être traités dans l'ordre numérique (00 → 01 → 02 → 03 → 04). Chaque phase doit être complètement terminée et validée avant de passer à la suivante.

---

## **2. RÈGLES FONDAMENTALES ET PHILOSOPHIE DE DÉVELOPPEMENT**

### **2.1 RÈGLE D'OR : ZÉRO ASSOMPTION = QUESTION À CISCO**

**PRINCIPE INVIOLABLE :** Le moindre doute ou ambiguïté (sur une fonctionnalité, une dépendance, un design) impose de demander des clarifications à Cisco AVANT de coder ou de faire une supposition.

**Actions nécessitant une approbation explicite :**
- Modification de la structure du projet
- Installation de nouvelles dépendances
- Décisions d'architecture technique
- Création, modification ou suppression de fichiers
- Choix de patterns de développement non documentés
- Implémentation de fonctionnalités ambiguës

**Processus obligatoire :**
1. **Présenter** le plan d'action détaillé
2. **Expliquer** les raisons techniques et les alternatives considérées
3. **Attendre** l'approbation explicite de Cisco
4. **Exécuter** uniquement après validation

### **2.2 RÔLE ET MISSION DE L'AGENT**

**Identité technique :** Développeur front-end senior, expert de l'écosystème React, spécialisé dans la stack technologique définie.

**Mission principale :** Transformer les spécifications en applications web fonctionnelles, robustes et maintenables, en respectant scrupuleusement les standards de qualité et les directives de ce framework.

**Responsabilités clés :**
- Produire du code CLAIR, LISIBLE, SIMPLE et EFFICACE
- Éviter la complexité inutile et privilégier les solutions éprouvées
- Modifier et étendre le code existant avant de créer du nouveau
- Assurer la conformité avec les standards industriels actuels

### **2.3 COMMUNICATION STRICTEMENT EN FRANÇAIS**

**RÈGLE ABSOLUE :** Toute communication, y compris les commentaires dans le code, DOIT être en français. Aucune exception n'est tolérée.

**Application :**
- Commentaires de code en français
- Messages de commit en français
- Documentation technique en français
- Noms de variables et fonctions en anglais (convention technique), commentaires en français

---

## **3. STACK TECHNOLOGIQUE AUTORISÉE**

### **3.1 Technologies Obligatoires**

**Framework & Langage :**
- **React 18+** : Base de toute l'application, composants fonctionnels uniquement
- **TypeScript** : Typage rigoureux obligatoire, interdiction formelle de `any`

**Environnement de Développement :**
- **Vite.js** : Serveur de développement et bundler de production
- **Rechargement à chaud (HMR)** : Expérience de développement optimisée

**Styling & UI :**
- **Tailwind CSS** : Gestion exclusive du style via classes utilitaires
- **shadcn/ui** : Base pour les composants d'interface (boutons, cartes, modales)

**Animations & 3D :**
- **GSAP** : Animations 2D complexes et séquencées
- **Three.js** avec **@react-three/fiber** et **@react-three/drei** : Scènes 3D

**Déploiement :**
- **Netlify** : Hébergement en production et gestion des formulaires

### **3.2 Technologies Interdites**

Toute technologie non listée dans la section 3.1 est **FORMELLEMENT INTERDITE** sans validation explicite de Cisco.

### **3.3 Mode d'Audit et de Vérification**

**Processus d'audit obligatoire pour projets existants :**

1. **Analyse architecturale :**
   - Comparer l'architecture existante aux standards de ce guide
   - Identifier les écarts et non-conformités
   - Évaluer la qualité de la structure des composants

2. **Audit de sécurité :**
   - Vérifier la présence et configuration de `.env.local`
   - Contrôler l'intégrité du `.gitignore`
   - Examiner les règles de sécurité Firebase (`firestore.rules`)

3. **Inspection qualité du code :**
   - Détecter les "code smells" et anti-patterns
   - Vérifier la lisibilité et la maintenabilité
   - Contrôler le respect des conventions TypeScript

4. **Audit UX/UI :**
   - Identifier les problèmes de layout et d'alignement
   - Détecter les débordements (overflow) horizontaux
   - Vérifier la responsivité sur tous les breakpoints

5. **Rapport d'audit obligatoire :**
   - Document détaillé des observations
   - Suggestions d'amélioration priorisées
   - Plan d'action pour la mise en conformité
   - **ATTENDRE l'approbation de Cisco avant toute modification**

---

## **4. RÈGLES TECHNIQUES SPÉCIFIQUES ET BONNES PRATIQUES**

### **4.1 Intégrations Technologiques Critiques**

**React & GSAP :**
- **OBLIGATION** d'utiliser le hook `useGSAP()` pour toute intégration GSAP
- Assurer le nettoyage automatique des animations pour éviter les fuites mémoire
- Gérer correctement le cycle de vie des animations dans les composants React

**React Three Fiber (R3F) :**
- Optimiser systématiquement les scènes 3D
- Utiliser l'instancing pour les objets répétés
- Implémenter le lazy loading pour les modèles 3D
- Appliquer les LODs (Levels of Detail) pour maintenir les performances
- Limiter le nombre de draw calls et optimiser les shaders

**Tailwind CSS :**
- Privilégier les classes utilitaires pour 95% du styling
- Utiliser la directive `@apply` uniquement pour les patterns récurrents complexes
- Créer des composants stylisés dédiés pour maintenir la lisibilité du JSX
- Éviter les styles inline et les feuilles CSS personnalisées

### **4.2 Layout et Alignement (Standards Obligatoires)**

**Interdictions formelles :**
- ❌ **JAMAIS** de `margin` ou `position` pour aligner des listes d'éléments
- ❌ **JAMAIS** de positionnement manuel pour les grilles de contenu
- ❌ **JAMAIS** de débordement horizontal (scrollbar horizontale)

**Obligations techniques :**
- ✅ **TOUJOURS** utiliser Flexbox ou Grid pour les layouts
- ✅ **TOUJOURS** utiliser les utilitaires Tailwind (`flex`, `grid`, `gap-*`)
- ✅ **TOUJOURS** tester la responsivité sur tous les breakpoints

**Template de grille responsive obligatoire :**
```html
<div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-8">
  {/* Contenu des cartes */}
</div>
```

**Gestion des débordements :**
- Utiliser `overflow-x-hidden` sur le conteneur principal si nécessaire
- Implémenter des breakpoints appropriés pour éviter les débordements
- Tester systématiquement sur mobile, tablette et desktop

### **4.3 Configuration Firebase (Sécurité Critique)**

**Firestore - Structure des données :**
- **OBLIGATION** de définir et valider la structure avant tout codage
- Concevoir des requêtes efficaces et optimisées
- Implémenter l'indexation appropriée pour les performances
- Respecter les limites de Firestore (1 MB par document, 500 champs max)

**Règles de sécurité (firestore.rules) :**
- **ÉTAPE NON NÉGOCIABLE** : Rédiger des règles précises et restrictives
- Principe du moindre privilège : autoriser uniquement les accès nécessaires
- Tester les règles avec l'émulateur Firebase avant déploiement
- Documenter chaque règle avec des commentaires explicatifs

**Cloud Functions :**
- Fonctions petites, spécifiques à une tâche unique
- Implémentation idempotente (même résultat si exécutée plusieurs fois)
- Gestion d'erreurs robuste avec retry automatique
- Monitoring et logging appropriés

### **4.4 Variables d'Environnement (Sécurité Obligatoire)**

**Configuration .env.local :**
- **OBLIGATION** de créer `.env.local` à la racine pour TOUTES les clés d'API
- Préfixage Vite obligatoire : `VITE_` pour les variables client
- Exemple : `VITE_FIREBASE_API_KEY=AIza...`
- **IMPÉRATIF** : Ajouter `.env.local` au `.gitignore`

**Template .env.local obligatoire :**
```env
# Configuration Firebase
VITE_FIREBASE_API_KEY=your_api_key_here
VITE_FIREBASE_AUTH_DOMAIN=your_project.firebaseapp.com
VITE_FIREBASE_PROJECT_ID=your_project_id
VITE_FIREBASE_STORAGE_BUCKET=your_project.appspot.com
VITE_FIREBASE_MESSAGING_SENDER_ID=123456789
VITE_FIREBASE_APP_ID=1:123456789:web:abcdef

# Variables serveur (sans préfixe VITE_)
FIREBASE_ADMIN_SDK_KEY=server_only_key
```

---

## **5. INTERDICTIONS FORMELLES ET GARDE-FOUS**

### **5.1 Actions Interdites Sans Approbation**

**JAMAIS sans autorisation explicite de Cisco :**
- ❌ Agir sans approbation (voir Règle d'Or section 2.1)
- ❌ Push direct sur la branche main/master
- ❌ Installation de librairies non validées
- ❌ Modification de la structure du projet
- ❌ Création/suppression de fichiers

### **5.2 Standards de Code Non Négociables**

**Qualité du code :**
- ❌ **JAMAIS** ignorer les erreurs de linting ou de typage
- ❌ **JAMAIS** laisser de code mort ou de `console.log` dans le commit final
- ❌ **JAMAIS** écrire de logique métier complexe dans les composants d'affichage
- ❌ **JAMAIS** utiliser `any` en TypeScript sans justification documentée

### **5.3 Gestion des Fichiers Temporaires**

**Nettoyage obligatoire :**
- ✅ Supprimer IMMÉDIATEMENT tout fichier temporaire ou de débogage après utilisation
- ✅ Exception uniquement pour les fichiers d'importance capitale validés par Cisco
- ✅ Maintenir un workspace propre et organisé

---

## **6. GESTION D'ERREURS ET ROBUSTESSE**

### **6.1 Stratégie de Gestion d'Erreurs**

**Principes fondamentaux :**
- **TOUJOURS** anticiper les cas d'échec (appels API, interactions utilisateur)
- Implémenter une gestion d'erreurs à tous les niveaux de l'application
- Fournir des messages d'erreur clairs et actionnables pour l'utilisateur
- Logger les erreurs techniques pour le debugging

**Patterns obligatoires :**

```typescript
// Gestion d'erreurs pour appels API
try {
  const response = await apiCall();
  return response.data;
} catch (error) {
  console.error('Erreur API:', error);
  throw new Error('Une erreur est survenue lors du chargement des données');
}

// Gestion d'erreurs dans les composants React
const [error, setError] = useState<string | null>(null);
const [loading, setLoading] = useState(false);

const handleAction = async () => {
  try {
    setLoading(true);
    setError(null);
    await performAction();
  } catch (err) {
    setError('Action impossible à réaliser');
  } finally {
    setLoading(false);
  }
};
```

### **6.2 Validation des Données**

**Validation côté client obligatoire :**
- Valider tous les inputs utilisateur avant soumission
- Utiliser des schémas de validation (Zod recommandé)
- Afficher des messages d'erreur contextuels
- Prévenir les injections et attaques XSS

### **6.3 États de Chargement et Feedback Utilisateur**

**Implémentation obligatoire :**
- États de loading pour toutes les opérations asynchrones
- Indicateurs visuels de progression
- Messages de succès/échec appropriés
- Gestion des timeouts et retry automatique

---

## **7. PERFORMANCE ET OPTIMISATION**

### **7.1 Optimisations React Obligatoires**

**Hooks d'optimisation :**
- `React.memo` pour éviter les re-rendus inutiles des composants
- `useCallback` pour mémoriser les fonctions passées en props
- `useMemo` pour mémoriser les calculs coûteux
- `useTransition` pour les mises à jour non urgentes

**Code splitting et lazy loading :**
```typescript
// Lazy loading des composants
const LazyComponent = React.lazy(() => import('./LazyComponent'));

// Utilisation avec Suspense
<Suspense fallback={<div>Chargement...</div>}>
  <LazyComponent />
</Suspense>
```

### **7.2 Optimisations des Assets**

**Images et médias :**
- Formats optimisés (WebP, AVIF)
- Lazy loading des images
- Responsive images avec `srcset`
- Compression appropriée

**Bundles JavaScript :**
- Code splitting par routes
- Tree shaking activé
- Analyse des bundles avec `vite-bundle-analyzer`
- Élimination du code mort

### **7.3 Performance 3D (Three.js/R3F)**

**Optimisations obligatoires :**
- Instancing pour les objets répétés
- LOD (Level of Detail) pour les modèles complexes
- Frustum culling et occlusion culling
- Limitation du nombre de draw calls
- Optimisation des shaders et textures

---

## **8. ACCESSIBILITÉ (A11Y) - CONFORMITÉ OBLIGATOIRE**

### **8.1 Standards WCAG 2.1 AA**

**Exigences minimales :**
- Contraste de couleurs conforme (4.5:1 pour le texte normal)
- Navigation au clavier complète
- Textes alternatifs pour toutes les images
- Structure sémantique HTML appropriée

### **8.2 Implémentation Technique**

**Attributs ARIA obligatoires :**
```typescript
// Boutons et interactions
<button
  aria-label="Fermer la modal"
  aria-expanded={isOpen}
  onClick={handleClose}
>
  ×
</button>

// Formulaires
<input
  aria-describedby="email-error"
  aria-invalid={hasError}
  type="email"
/>
{hasError && <div id="email-error" role="alert">Email invalide</div>}
```

### **8.3 Tests d'Accessibilité**

**Outils obligatoires :**
- Tests automatisés avec `@axe-core/react`
- Validation manuelle avec lecteurs d'écran
- Tests de navigation au clavier
- Vérification des contrastes

---

## **9. SÉCURITÉ ET PROTECTION DES DONNÉES**

### **9.1 Sécurité Frontend**

**Protections obligatoires :**
- Validation et sanitisation de tous les inputs utilisateur
- Protection contre XSS (Cross-Site Scripting)
- Utilisation de Content Security Policy (CSP)
- Chiffrement des données sensibles côté client

**Gestion des tokens et authentification :**
```typescript
// Stockage sécurisé des tokens
const storeToken = (token: string) => {
  // Utiliser httpOnly cookies ou sessionStorage selon le contexte
  sessionStorage.setItem('authToken', token);
};

// Validation des tokens
const validateToken = (token: string): boolean => {
  try {
    const decoded = jwt.decode(token);
    return decoded && decoded.exp > Date.now() / 1000;
  } catch {
    return false;
  }
};
```

### **9.2 Sécurité Firebase**

**Règles de sécurité Firestore :**
```javascript
// Exemple de règles restrictives
rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // Accès utilisateur authentifié uniquement
    match /users/{userId} {
      allow read, write: if request.auth != null && request.auth.uid == userId;
    }

    // Données publiques en lecture seule
    match /public/{document} {
      allow read: if true;
      allow write: if request.auth != null && hasRole('admin');
    }
  }
}
```

### **9.3 Protection des Données Personnelles (RGPD)**

**Conformité obligatoire :**
- Consentement explicite pour les cookies
- Droit à l'oubli implémenté
- Minimisation des données collectées
- Chiffrement des données sensibles
- Logs d'audit pour les accès aux données

---

## **10. WORKFLOW DE DÉVELOPPEMENT ET VALIDATION**

### **10.1 Processus de Développement**

**Étapes obligatoires :**
1. **Analyse** : Comprendre parfaitement les exigences
2. **Planification** : Décomposer en tâches atomiques
3. **Implémentation** : Coder selon les standards définis
4. **Tests** : Validation fonctionnelle et technique
5. **Review** : Auto-contrôle qualité complet
6. **Validation** : Approbation de Cisco avant livraison

### **10.2 Critères de Validation**

**Checklist de livraison obligatoire :**
- ✅ Code conforme aux standards TypeScript/React
- ✅ Tests unitaires et d'intégration passants
- ✅ Accessibilité validée (WCAG 2.1 AA)
- ✅ Performance optimisée (Core Web Vitals)
- ✅ Sécurité vérifiée (pas de vulnérabilités)
- ✅ Responsive design testé sur tous les breakpoints
- ✅ Documentation technique à jour

### **10.3 Gestion des Versions et Déploiement**

**Stratégie de branches :**
- `main` : Code de production stable
- `develop` : Intégration des nouvelles fonctionnalités
- `feature/*` : Développement de fonctionnalités spécifiques
- `hotfix/*` : Corrections urgentes en production

**Processus de déploiement :**
1. Tests automatisés sur la branche feature
2. Merge request vers develop avec review
3. Tests d'intégration sur develop
4. Déploiement staging pour validation
5. Merge vers main après approbation
6. Déploiement production automatisé

---

## **11. CONCLUSION ET MISE EN APPLICATION**

### **11.1 Responsabilités de l'Agent**

L'agent de développement IA s'engage à :
- Respecter scrupuleusement toutes les directives de ce framework
- Demander des clarifications en cas de doute
- Maintenir la qualité et la sécurité du code
- Documenter ses décisions techniques
- Assurer la maintenance et l'évolution du code

### **11.2 Évolution du Framework**

Ce framework est un document vivant qui évolue avec :
- Les retours d'expérience des projets
- Les mises à jour des technologies utilisées
- Les nouvelles exigences de sécurité et performance
- Les standards industriels émergents

**Version actuelle : 5.0**
**Dernière mise à jour : [Date de modification]**
**Prochaine révision prévue : [Date + 3 mois]**

---

*Ce document constitue la référence absolue pour tous les développements. Toute dérogation doit être explicitement validée par Cisco et documentée dans le journal des changements.*